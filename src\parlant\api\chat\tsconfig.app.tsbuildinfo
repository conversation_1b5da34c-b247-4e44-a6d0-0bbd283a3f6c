{"root": ["./src/app.tsx", "./src/main.tsx", "./src/store.ts", "./src/vite-env.d.ts", "./src/components/admin/admininterface.tsx", "./src/components/admin/adminlayout.tsx", "./src/components/admin/agentspage.tsx", "./src/components/admin/analyticspage.tsx", "./src/components/admin/backuppage.tsx", "./src/components/admin/llmconfigpage.tsx", "./src/components/admin/logspage.tsx", "./src/components/admin/systemsettingspage.tsx", "./src/components/admin/modals/agentmodal.tsx", "./src/components/admin/modals/agentviewmodal.tsx", "./src/components/admin/modals/llmprovidermodal.tsx", "./src/components/agents-list/agent-list.test.tsx", "./src/components/agents-list/agent-list.tsx", "./src/components/avatar/avatar.tsx", "./src/components/chat-header/chat-header.test.tsx", "./src/components/chat-header/chat-header.tsx", "./src/components/chatbot/chatbot.test.tsx", "./src/components/chatbot/chatbot.tsx", "./src/components/dark-mode-toggle/dark-mode-toggle.tsx", "./src/components/error-boundary/error-boundary.tsx", "./src/components/gradient-button/gradient-button.tsx", "./src/components/header-wrapper/header-wrapper.tsx", "./src/components/log-filters/log-filters.tsx", "./src/components/markdown/markdown.tsx", "./src/components/message/message-relative-time.tsx", "./src/components/message/message.test.tsx", "./src/components/message/message.tsx", "./src/components/message-details/empty-state.tsx", "./src/components/message-details/filter-tabs.tsx", "./src/components/message-details/indexeddb-data.tsx", "./src/components/message-details/message-details-header.tsx", "./src/components/message-details/message-details.tsx", "./src/components/message-details/message-log.tsx", "./src/components/message-details/message-logs.tsx", "./src/components/message-fragment/message-fragment.tsx", "./src/components/message-fragments/message-fragments.tsx", "./src/components/progress-logo/progress-logo.tsx", "./src/components/session-list/session-list.test.tsx", "./src/components/session-list/session-list.tsx", "./src/components/session-list/session-list-item/session-list-item.test.tsx", "./src/components/session-list/session-list-item/session-list-item.tsx", "./src/components/session-view/session-view.test.tsx", "./src/components/session-view/session-view.tsx", "./src/components/session-view/date-header/date-header.tsx", "./src/components/session-view/session-view-header/session-view-header.tsx", "./src/components/ui/button.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/drawer.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/radio-group.tsx", "./src/components/ui/resizable.tsx", "./src/components/ui/select.tsx", "./src/components/ui/sheet.tsx", "./src/components/ui/skeleton.tsx", "./src/components/ui/sonner.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/toast.tsx", "./src/components/ui/tooltip.tsx", "./src/components/ui/code/code-block.tsx", "./src/components/ui/code/diff-viewer.tsx", "./src/components/ui/custom/copy-text.tsx", "./src/components/ui/custom/line-no-div.tsx", "./src/components/ui/custom/spacer.tsx", "./src/components/ui/custom/tooltip.tsx", "./src/components/ui/debug/call-stack.tsx", "./src/components/ui/debug/inspector.tsx", "./src/components/ui/terminal/terminal.tsx", "./src/components/virtual-scroll/virtual-scroll.tsx", "./src/hooks/useagents.ts", "./src/hooks/usedialog.test.tsx", "./src/hooks/usedialog.tsx", "./src/hooks/usefetch.tsx", "./src/hooks/uselocalstorage.ts", "./src/hooks/usequestiondialog.tsx", "./src/hooks/usesessions.ts", "./src/hooks/usesystemstats.ts", "./src/hooks/usewebsocket.ts", "./src/lib/broadcast-channel.ts", "./src/lib/utils.ts", "./src/pages/admin/agentsmanagement.tsx", "./src/pages/admin/configuration.tsx", "./src/pages/admin/dashboard.tsx", "./src/pages/admin/datamanagement.tsx", "./src/pages/admin/monitoring.tsx", "./src/pages/admin/settings.tsx", "./src/pages/admin/setupwizard.tsx", "./src/utils/api.ts", "./src/utils/date.tsx", "./src/utils/interfaces.tsx", "./src/utils/logs.ts", "./src/utils/methods.tsx", "./src/utils/obj.tsx"], "errors": true, "version": "5.8.3"}