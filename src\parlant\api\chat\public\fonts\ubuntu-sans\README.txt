Ubuntu Sans Variable Font
=========================

This download contains Ubuntu Sans as both variable fonts and static fonts.

Ubuntu Sans is a variable font with these axes:
  wdth
  wght

This means all the styles are contained in these files:
  Ubuntu_Sans/UbuntuSans-VariableFont_wdth,wght.ttf
  Ubuntu_Sans/UbuntuSans-Italic-VariableFont_wdth,wght.ttf

If your app fully supports variable fonts, you can now pick intermediate styles
that aren’t available as static fonts. Not all apps support variable fonts, and
in those cases you can use the static font files for Ubuntu Sans:
  Ubuntu_Sans/static/UbuntuSans_Condensed-Thin.ttf
  Ubuntu_Sans/static/UbuntuSans_Condensed-ExtraLight.ttf
  Ubuntu_Sans/static/UbuntuSans_Condensed-Light.ttf
  Ubuntu_Sans/static/UbuntuSans_Condensed-Regular.ttf
  Ubuntu_Sans/static/UbuntuSans_Condensed-Medium.ttf
  Ubuntu_Sans/static/UbuntuSans_Condensed-SemiBold.ttf
  Ubuntu_Sans/static/UbuntuSans_Condensed-Bold.ttf
  Ubuntu_Sans/static/UbuntuSans_Condensed-ExtraBold.ttf
  Ubuntu_Sans/static/UbuntuSans_SemiCondensed-Thin.ttf
  Ubuntu_Sans/static/UbuntuSans_SemiCondensed-ExtraLight.ttf
  Ubuntu_Sans/static/UbuntuSans_SemiCondensed-Light.ttf
  Ubuntu_Sans/static/UbuntuSans_SemiCondensed-Regular.ttf
  Ubuntu_Sans/static/UbuntuSans_SemiCondensed-Medium.ttf
  Ubuntu_Sans/static/UbuntuSans_SemiCondensed-SemiBold.ttf
  Ubuntu_Sans/static/UbuntuSans_SemiCondensed-Bold.ttf
  Ubuntu_Sans/static/UbuntuSans_SemiCondensed-ExtraBold.ttf
  Ubuntu_Sans/static/UbuntuSans-Thin.ttf
  Ubuntu_Sans/static/UbuntuSans-ExtraLight.ttf
  Ubuntu_Sans/static/UbuntuSans-Light.ttf
  Ubuntu_Sans/static/UbuntuSans-Regular.ttf
  Ubuntu_Sans/static/UbuntuSans-Medium.ttf
  Ubuntu_Sans/static/UbuntuSans-SemiBold.ttf
  Ubuntu_Sans/static/UbuntuSans-Bold.ttf
  Ubuntu_Sans/static/UbuntuSans-ExtraBold.ttf
  Ubuntu_Sans/static/UbuntuSans_Condensed-ThinItalic.ttf
  Ubuntu_Sans/static/UbuntuSans_Condensed-ExtraLightItalic.ttf
  Ubuntu_Sans/static/UbuntuSans_Condensed-LightItalic.ttf
  Ubuntu_Sans/static/UbuntuSans_Condensed-Italic.ttf
  Ubuntu_Sans/static/UbuntuSans_Condensed-MediumItalic.ttf
  Ubuntu_Sans/static/UbuntuSans_Condensed-SemiBoldItalic.ttf
  Ubuntu_Sans/static/UbuntuSans_Condensed-BoldItalic.ttf
  Ubuntu_Sans/static/UbuntuSans_Condensed-ExtraBoldItalic.ttf
  Ubuntu_Sans/static/UbuntuSans_SemiCondensed-ThinItalic.ttf
  Ubuntu_Sans/static/UbuntuSans_SemiCondensed-ExtraLightItalic.ttf
  Ubuntu_Sans/static/UbuntuSans_SemiCondensed-LightItalic.ttf
  Ubuntu_Sans/static/UbuntuSans_SemiCondensed-Italic.ttf
  Ubuntu_Sans/static/UbuntuSans_SemiCondensed-MediumItalic.ttf
  Ubuntu_Sans/static/UbuntuSans_SemiCondensed-SemiBoldItalic.ttf
  Ubuntu_Sans/static/UbuntuSans_SemiCondensed-BoldItalic.ttf
  Ubuntu_Sans/static/UbuntuSans_SemiCondensed-ExtraBoldItalic.ttf
  Ubuntu_Sans/static/UbuntuSans-ThinItalic.ttf
  Ubuntu_Sans/static/UbuntuSans-ExtraLightItalic.ttf
  Ubuntu_Sans/static/UbuntuSans-LightItalic.ttf
  Ubuntu_Sans/static/UbuntuSans-Italic.ttf
  Ubuntu_Sans/static/UbuntuSans-MediumItalic.ttf
  Ubuntu_Sans/static/UbuntuSans-SemiBoldItalic.ttf
  Ubuntu_Sans/static/UbuntuSans-BoldItalic.ttf
  Ubuntu_Sans/static/UbuntuSans-ExtraBoldItalic.ttf

Get started
-----------

1. Install the font files you want to use

2. Use your app's font picker to view the font family and all the
available styles

Learn more about variable fonts
-------------------------------

  https://developers.google.com/web/fundamentals/design-and-ux/typography/variable-fonts
  https://variablefonts.typenetwork.com
  https://medium.com/variable-fonts

In desktop apps

  https://theblog.adobe.com/can-variable-fonts-illustrator-cc
  https://helpx.adobe.com/nz/photoshop/using/fonts.html#variable_fonts

Online

  https://developers.google.com/fonts/docs/getting_started
  https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Fonts/Variable_Fonts_Guide
  https://developer.microsoft.com/en-us/microsoft-edge/testdrive/demos/variable-fonts

Installing fonts

  MacOS: https://support.apple.com/en-us/HT201749
  Linux: https://www.google.com/search?q=how+to+install+a+font+on+gnu%2Blinux
  Windows: https://support.microsoft.com/en-us/help/314960/how-to-install-or-remove-a-font-in-windows

Android Apps

  https://developers.google.com/fonts/docs/android
  https://developer.android.com/guide/topics/ui/look-and-feel/downloadable-fonts

License
-------
Please read the full license text (UFL.txt) to understand the permissions,
restrictions and requirements for usage, redistribution, and modification.

You can use them in your products & projects – print or digital,
commercial or otherwise.

This isn't legal advice, please consider consulting a lawyer and see the full
license for all details.
