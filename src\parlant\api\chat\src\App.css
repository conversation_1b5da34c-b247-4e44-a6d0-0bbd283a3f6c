#root {
	height: 100vh;
	margin: auto;
	font-family: 'Inter';
}

body {
	pointer-events: all !important;
}

.fixed-scroll {
	overflow: scroll;
	scrollbar-width: thin;
	scrollbar-color: #ebecf0 transparent;
}

.fixed-scroll:hover {
	scrollbar-color: #cdcdcd transparent;
}

.fixed-scroll::-webkit-scrollbar {
	width: 10px;
}

.fixed-scroll::-webkit-scrollbar-thumb {
	background-color: rgba(0, 0, 0, 0.5);
	border-radius: 10px;
}

.fixed-scroll::-webkit-scrollbar-track {
	background: transparent;
}

.markdown * {
	font-size: revert;
	font-weight: revert;
	padding: revert;
	margin: revert;
	list-style-type: revert;
	color: revert;
	text-decoration: revert;
}

img {
	user-select: none;
}
