{
  "name": "poetry3-poetry-pyenv",
  "build": {
    "dockerfile": "Dockerfile"
  },

  // "features": {},

  // 👇 Use 'forwardPorts' to make a list of ports inside the container available locally.
  // "forwardPorts": [],

  // 👇 Use 'postCreateCommand' to run commands after the container is created.
  "postCreateCommand": "(cd /workspaces/parlant && git config --global --add safe.directory /workspaces/parlant && python ./initialize_repo.py)",

  // 👇 Configure tool-specific properties.
  "customizations": {
    "vscode": {
      "extensions": [
        "alexkrechik.cucumberautocomplete",
        "charliermarsh.ruff",
        "github.remotehub",
        "github.vscode-github-actions",
        "GitHub.vscode-pull-request-github",
        "hbenl.vscode-test-explorer",
        "matangover.mypy",
        "ms-azuretools.vscode-docker",
        "ms-python.debugpy",
        "ms-python.python",
        "ms-python.vscode-pylance",
        "mutantdino.resourcemonitor",
        "njpwerner.autodocstring",
        "tamasfe.even-better-toml",
        "zeshuaro.vscode-python-poetry"
      ]
    }
  },
  // 👇 Features to add to the Dev Container. More info: https://containers.dev/implementors/features.
  "features": {
    "ghcr.io/devcontainers-contrib/features/mypy:2": {
      "version": "latest"
    },
    "ghcr.io/devcontainers-contrib/features/poetry:2": {
      "version": "latest"
    },
    "node": {
        "version": "lts",
        "nodeGypDependencies": true
    }
  },

  "mounts": []

  // 👇 Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
  // "remoteUser": "root"
}
