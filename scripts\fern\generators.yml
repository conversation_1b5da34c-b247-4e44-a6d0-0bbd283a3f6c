api:
  specs:
    - openapi: openapi/parlant.openapi.json
default-group: local
groups:
  local:
    generators:
      - name: fernapi/fern-typescript-node-sdk
        version: 0.49.2
        config:
          namespaceExport: Parlant
        output:
          location: local-file-system
          path: ../sdks/typescript
      - name: fernapi/fern-python-sdk
        version: 4.3.3
        config:
          client_class_name: ParlantClient
        output:
          location: local-file-system
          path: ../sdks/python
