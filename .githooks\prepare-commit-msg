#!/bin/bash

# A git commit hook that will automatically append a DCO signoff to the bottom
# of any commit message that does not have one. This append happens after the git
# default message is generated, but before the user is dropped into the commit
# message editor.
ROOT=$(git rev-parse --show-toplevel)
cd $ROOT

COMMIT_MESSAGE_FILE="$1"
AUTHOR=$(git var GIT_AUTHOR_IDENT)
SIGNOFF=$(echo "$AUTHOR" | sed -n 's/^\(.*>\).*$/Signed-off-by: \1/p')

# Check for DCO signoff message. If one does not exist, append one and then warn
# the user that you did so.
if ! grep -qs "^$SIGNOFF" "$COMMIT_MESSAGE_FILE"; then
  echo -e "\n$SIGNOFF" >> "$COMMIT_MESSAGE_FILE"
  echo -e "Appended the following signoff to the end of the commit message:\n  $SIGNOFF\n"
fi