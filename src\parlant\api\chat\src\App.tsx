import './App.css';
import React, { useState } from 'react';
import Chatbot from './components/chatbot/chatbot';
import AdminInterface from './components/admin/AdminInterface';
import { ToastProvider } from './components/ui/toast';
import {useWebSocket} from './hooks/useWebSocket';
import {BASE_URL} from './utils/api';
import {handleChatLogs} from './utils/logs';
// Import the new enhanced chat interface
import EnhancedChatInterface from './components/enhanced-chat/enhanced-chat-interface';
import ChatImprovementsDemo from './components/demo/chat-improvements-demo';

const WebSocketComp = () => {
	const socket = useWebSocket(`${BASE_URL}/logs`, true, null, handleChatLogs);
	void socket;
	return <div></div>;
};

function App() {
	const [currentView, setCurrentView] = useState<'chat' | 'admin' | 'enhanced' | 'demo'>('chat');

	// Check URL to determine initial view
	React.useEffect(() => {
		const path = window.location.pathname;
		if (path.includes('admin')) {
			setCurrentView('admin');
		} else if (path.includes('enhanced')) {
			setCurrentView('enhanced');
		} else if (path.includes('demo')) {
			setCurrentView('demo');
		} else {
			setCurrentView('chat');
		}
	}, []);

	// Update URL when view changes
	React.useEffect(() => {
		let newPath = '/chat/';
		if (currentView === 'admin') newPath = '/chat/admin';
		else if (currentView === 'enhanced') newPath = '/chat/enhanced';
		else if (currentView === 'demo') newPath = '/chat/demo';

		if (window.location.pathname !== newPath) {
			window.history.pushState({}, '', newPath);
		}
	}, [currentView]);

	return (
		<ToastProvider>
			<div className='bg-blue-light min-h-screen'>
				{currentView === 'chat' ? (
					<>
						<Chatbot
							onNavigateToAdmin={() => setCurrentView('admin')}
							onNavigateToEnhanced={() => setCurrentView('enhanced')}
							onNavigateToDemo={() => setCurrentView('demo')}
						/>
						<WebSocketComp />
					</>
				) : currentView === 'enhanced' ? (
					<EnhancedChatInterface
						onNavigateToAdmin={() => setCurrentView('admin')}
						onNavigateToChat={() => setCurrentView('chat')}
						onNavigateToDemo={() => setCurrentView('demo')}
					/>
				) : currentView === 'demo' ? (
					<ChatImprovementsDemo
						onNavigateToChat={() => setCurrentView('chat')}
						onNavigateToEnhanced={() => setCurrentView('enhanced')}
						onNavigateToAdmin={() => setCurrentView('admin')}
					/>
				) : (
					<AdminInterface onNavigateToChat={() => setCurrentView('chat')} />
				)}
			</div>
		</ToastProvider>
	);
}

export default App;
