# Copyright 2025 Emcie Co Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from dataclasses import dataclass
from itertools import chain
import json
import traceback
from typing import Any, Mapping, Optional, Sequence, cast

from parlant.core.contextual_correlator import ContextualCorrelator
from parlant.core.agents import Agent
from parlant.core.context_variables import ContextVariable, ContextVariableValue
from parlant.core.customers import Customer
from parlant.core.engines.alpha.message_event_composer import (
    MessageCompositionError,
    MessageEventComposer,
    MessageEventComposition,
)
from parlant.core.engines.alpha.tool_caller import Missing<PERSON><PERSON><PERSON><PERSON>, ToolInsights
from parlant.core.nlp.generation import SchematicGenerator
from parlant.core.nlp.generation_info import GenerationInfo
from parlant.core.engines.alpha.guideline_match import GuidelineMatch
from parlant.core.engines.alpha.prompt_builder import PromptBuilder, SectionStatus
from parlant.core.glossary import Term
from parlant.core.emissions import EmittedEvent, EventEmitter
from parlant.core.sessions import Event, EventKind, EventSource
from parlant.core.common import DefaultBaseModel
from parlant.core.loggers import Logger
from parlant.core.shots import Shot, ShotCollection
from parlant.core.tools import ToolId


class ContextEvaluation(DefaultBaseModel):
    most_recent_customer_inquiries_or_needs: Optional[str] = None
    parts_of_the_context_i_have_here_if_any_with_specific_information_on_how_to_address_these_needs: Optional[
        str
    ] = None
    topics_for_which_i_have_sufficient_information_and_can_therefore_help_with: Optional[str] = None
    what_i_do_not_have_enough_information_to_help_with_with_based_on_the_provided_information_that_i_have: Optional[
        str
    ] = None
    was_i_given_specific_information_here_on_how_to_address_some_of_these_specific_needs: Optional[
        bool
    ] = None
    should_i_tell_the_customer_i_cannot_help_with_some_of_those_needs: Optional[bool] = None


class FactualInformationEvaluation(DefaultBaseModel):
    fact: str
    source: str
    is_source_based_in_this_prompt: bool


class OfferedServiceEvaluation(DefaultBaseModel):
    service: str
    source: str
    is_source_based_in_this_prompt: bool


class Revision(DefaultBaseModel):
    revision_number: int
    content: str
    factual_information_provided: Optional[list[FactualInformationEvaluation]] = None
    offered_services: Optional[list[OfferedServiceEvaluation]] = None
    instructions_followed: Optional[list[str]] = None
    instructions_broken: Optional[list[str]] = None
    is_repeat_message: Optional[bool] = None
    followed_all_instructions: Optional[bool] = None
    instructions_broken_due_to_missing_data: Optional[bool] = None
    missing_data_rationale: Optional[str] = None
    instructions_broken_only_due_to_prioritization: Optional[bool] = None
    prioritization_rationale: Optional[str] = None
    all_facts_and_services_sourced_from_prompt: Optional[bool] = None
    further_revisions_required: Optional[bool] = None


class InstructionEvaluation(DefaultBaseModel):
    number: int
    instruction: str
    evaluation: str
    data_available: str


class MessageSchema(DefaultBaseModel):
    last_message_of_customer: Optional[str] = None
    produced_reply: Optional[bool] = None
    produced_reply_rationale: Optional[str] = None
    guidelines: Optional[list[str]] = None
    context_evaluation: Optional[ContextEvaluation] = None
    insights: Optional[list[str]] = None
    evaluation_for_each_instruction: Optional[list[InstructionEvaluation]] = None
    revisions: Optional[list[Revision]] = None


@dataclass
class MessageGeneratorShot(Shot):
    expected_result: MessageSchema


class MessageGenerator(MessageEventComposer):
    def __init__(
        self,
        logger: Logger,
        correlator: ContextualCorrelator,
        schematic_generator: SchematicGenerator[MessageSchema],
    ) -> None:
        self._logger = logger
        self._correlator = correlator
        self._schematic_generator = schematic_generator

    async def shots(self) -> Sequence[MessageGeneratorShot]:
        return await shot_collection.list()

    async def generate_events(
        self,
        event_emitter: EventEmitter,
        agent: Agent,
        customer: Customer,
        context_variables: Sequence[tuple[ContextVariable, ContextVariableValue]],
        interaction_history: Sequence[Event],
        terms: Sequence[Term],
        ordinary_guideline_matches: Sequence[GuidelineMatch],
        tool_enabled_guideline_matches: Mapping[GuidelineMatch, Sequence[ToolId]],
        tool_insights: ToolInsights,
        staged_events: Sequence[EmittedEvent],
    ) -> Sequence[MessageEventComposition]:
        with self._logger.scope("MessageEventComposer"):
            with self._logger.scope("MessageGenerator"):
                with self._logger.operation("Message generation"):
                    return await self._do_generate_events(
                        event_emitter,
                        agent,
                        customer,
                        context_variables,
                        interaction_history,
                        terms,
                        ordinary_guideline_matches,
                        tool_enabled_guideline_matches,
                        tool_insights,
                        staged_events,
                    )

    def _format_staged_events(
        self,
        staged_events: Sequence[EmittedEvent],
    ) -> Sequence[EmittedEvent]:
        for event in staged_events:
            if event.kind == EventKind.TOOL:
                event_data: dict[str, Any] = cast(dict[str, Any], event.data)
                tool_calls: list[Any] = cast(list[Any], event_data.get("tool_calls", []))
                for tool_call in tool_calls:
                    if "utterances" in tool_call.get("result", {}):
                        del tool_call["result"]["utterances"]

        return staged_events

    async def _do_generate_events(
        self,
        event_emitter: EventEmitter,
        agent: Agent,
        customer: Customer,
        context_variables: Sequence[tuple[ContextVariable, ContextVariableValue]],
        interaction_history: Sequence[Event],
        terms: Sequence[Term],
        ordinary_guideline_matches: Sequence[GuidelineMatch],
        tool_enabled_guideline_matches: Mapping[GuidelineMatch, Sequence[ToolId]],
        tool_insights: ToolInsights,
        staged_events: Sequence[EmittedEvent],
    ) -> Sequence[MessageEventComposition]:
        if (
            not interaction_history
            and not ordinary_guideline_matches
            and not tool_enabled_guideline_matches
        ):
            # No interaction and no guidelines that could trigger
            # a proactive start of the interaction
            self._logger.info("Skipping response; interaction is empty and there are no guidelines")
            return []

        prompt = self._build_prompt(
            agent=agent,
            context_variables=context_variables,
            customer=customer,
            interaction_history=interaction_history,
            terms=terms,
            ordinary_guideline_matches=ordinary_guideline_matches,
            tool_enabled_guideline_matches=tool_enabled_guideline_matches,
            staged_events=staged_events,
            tool_insights=tool_insights,
            shots=await self.shots(),
        )

        last_known_event_offset = interaction_history[-1].offset if interaction_history else -1

        await event_emitter.emit_status_event(
            correlation_id=self._correlator.correlation_id,
            data={
                "acknowledged_offset": last_known_event_offset,
                "status": "typing",
                "data": {},
            },
        )

        generation_attempt_temperatures = {
            0: 0.1,
            1: 0.3,
            2: 0.5,
        }

        last_generation_exception: Exception | None = None

        for generation_attempt in range(3):
            try:
                generation_info, response_message = await self._generate_response_message(
                    prompt,
                    temperature=generation_attempt_temperatures[generation_attempt],
                    final_attempt=(generation_attempt + 1) == len(generation_attempt_temperatures),
                )

                if response_message is not None:
                    event = await event_emitter.emit_message_event(
                        correlation_id=self._correlator.correlation_id,
                        data=response_message,
                    )

                    return [MessageEventComposition(generation_info, [event])]
                else:
                    self._logger.debug("Skipping response; no response deemed necessary")
                    return [MessageEventComposition(generation_info, [])]
            except Exception as exc:
                self._logger.warning(
                    f"Generation attempt {generation_attempt} failed: {traceback.format_exception(exc)}"
                )
                last_generation_exception = exc

        raise MessageCompositionError() from last_generation_exception

    def get_guideline_matches_text(
        self,
        ordinary: Sequence[GuidelineMatch],
        tool_enabled: Mapping[GuidelineMatch, Sequence[ToolId]],
    ) -> tuple[str, dict[str, Any]]:
        all_matches = list(chain(ordinary, tool_enabled))

        if not all_matches:
            return (
                """
In formulating your reply, you are normally required to follow a number of behavioral guidelines.
However, in this case, no special behavioral guidelines were provided. Therefore, when generating revisions,
you don't need to specifically double-check if you followed or broke any guidelines.
""",
                {},
            )
        guidelines = []

        for i, p in enumerate(all_matches, start=1):
            guideline = f"Guideline #{i}) When {p.guideline.content.condition}, then {p.guideline.content.action}"

            guideline += f"\n    [Priority (1-10): {p.score}; Rationale: {p.rationale}]"
            guidelines.append(guideline)

        guideline_list = "\n".join(guidelines)

        return (
            """
When crafting your reply, you must follow the behavioral guidelines provided below, which have been identified as relevant to the current state of the interaction.
These guidelines were provided by the business you are representing.
Each guideline includes a priority score to indicate its importance and a rationale for its relevance.

You may choose not to follow a guideline only in the following cases:
    - It conflicts with a previous customer request.
    - It contradicts another guideline of equal or higher priority.
    - It is clearly inappropriate given the current context of the conversation.
In all other situations, you are expected to adhere to the guidelines.
These guidelines have already been pre-filtered based on the interaction's context and other considerations outside your scope.
Do not disregard a guideline because you believe its 'when' condition or rationale does not apply—this filtering has already been handled.

- **Guidelines**:
{guideline_list}
""",
            {"guideline_list": guideline_list},
        )

    def _format_shots(self, shots: Sequence[MessageGeneratorShot]) -> str:
        return "\n".join(
            f"""
    Example {i} - {shot.description}: ###
    {self._format_shot(shot)}
    ###"""
            for i, shot in enumerate(shots, 1)
        )

    def _format_shot(
        self,
        shot: MessageGeneratorShot,
    ) -> str:
        return f"""
- **Expected Result**:
```json
{json.dumps(shot.expected_result.model_dump(mode="json", exclude_unset=True), indent=2)}
```"""

    def _build_prompt(
        self,
        agent: Agent,
        customer: Customer,
        context_variables: Sequence[tuple[ContextVariable, ContextVariableValue]],
        interaction_history: Sequence[Event],
        terms: Sequence[Term],
        ordinary_guideline_matches: Sequence[GuidelineMatch],
        tool_enabled_guideline_matches: Mapping[GuidelineMatch, Sequence[ToolId]],
        staged_events: Sequence[EmittedEvent],
        tool_insights: ToolInsights,
        shots: Sequence[MessageGeneratorShot],
    ) -> PromptBuilder:
        builder = PromptBuilder(on_build=lambda prompt: self._logger.debug(f"Prompt:\n{prompt}"))

        builder.add_section(
            name="message-generator-general-instructions",
            template="""
GENERAL INSTRUCTIONS
-----------------
You are an AI agent who is part of a system that interacts with a user. The current state of this interaction will be provided to you later in this message.
You role is to generate a reply message to the current (latest) state of the interaction, based on provided guidelines and background information.

Later in this prompt, you'll be provided with behavioral guidelines and other contextual information you must take into account when generating your response.

""",
            props={},
        )

        builder.add_agent_identity(agent)
        builder.add_customer_identity(customer)
        builder.add_section(
            name="message-generator-task-description",
            template="""
TASK DESCRIPTION:
-----------------
Continue the provided interaction in a natural and human-like manner.
Your task is to produce a response to the latest state of the interaction.
Always abide by the following general principles (note these are not the "guidelines". The guidelines will be provided later):
1. GENERAL BEHAVIOR: Craft responses that feel natural and human-like. Keep them concise and polite, striking a balance between warmth and brevity without becoming overly verbose.
2. AVOID REPEATING YOURSELF: When replying— avoid repeating yourself. Instead, refer the customer to your previous answer, or choose a new approach altogether. If a conversation is looping, point that out to the customer instead of maintaining the loop.
3. DO NOT HALLUCINATE: Do not state factual information that you do not know or are not sure about. If the customer requests information you're unsure about, state that this information is not available to you.
4. ONLY OFFER SERVICES AND INFORMATION PROVIDED IN THIS PROMPT: Do not output information or offer services based on your intrinsic knowledge - you must only represent the business according to the information provided in this prompt.
5. REITERATE INFORMATION FROM PREVIOUS MESSAGES IF NECESSARY: If you previously suggested a solution, a recommendation, or any other information, you may repeat it when relevant. Your earlier response may have been based on information that is no longer available to you, so it’s important to trust that it was informed by the context at the time.
6. MAINTAIN GENERATION SECRECY: Never reveal details about the process you followed to produce your response. Do not explicitly mention the tools, context variables, guidelines, glossary, or any other internal information. Present your replies as though all relevant knowledge is inherent to you, not derived from external instructions.
7. OUTPUT FORMAT: In your generated reply to the customer, use markdown format when applicable.
""",
            props={},
        )
        if not interaction_history or all(
            [event.kind != EventKind.MESSAGE for event in interaction_history]
        ):
            builder.add_section(
                name="message-generator-initial-message-instructions",
                template="""
The interaction with the customer has just began, and no messages were sent by either party.
If told so by a guideline or some other contextual condition, send the first message. Otherwise, do not produce a reply.
If you decide not to emit a message, output the following:
{{
    "last_message_of_customer": None,
    "produced_reply": false,
    "guidelines": [<list of strings- a re-statement of all guidelines>],
    "context_evaluation": None,
    "insights": [<list of strings- up to 3 original insights>],
    "produced_reply_rationale": "<a few words to justify why a reply was NOT produced here>",
    "revisions": []
}}
Otherwise, follow the rest of this prompt to choose the content of your response.
        """,
                props={},
            )

        else:
            builder.add_section(
                name="message-generator-ongoing-interaction-instructions",
                template="""
Since the interaction with the customer is already ongoing, always produce a reply to the customer's last message.
The only exception where you may not produce a reply is if the customer explicitly asked you not to respond to their message.
In all other cases, even if the customer is indicating that the conversation is over, you must produce a reply.
                """,
                props={},
            )

        builder.add_section(
            name="message-generator-revision-mechanism",
            template="""
REVISION MECHANISM
-----------------
To generate an optimal response that aligns with all guidelines and the current interaction state, follow this structured revision process:

1. INSIGHT GATHERING (Pre-Revision)
   - Before starting revisions, identify up to three key insights from:
     * Explicit or implicit customer requests
     * Relevant principles from this prompt
     * Notable patterns or conclusions from the interaction
   - Each insight should be actionable and directly relevant to crafting the response
   - Only include absolutely necessary insights; fewer is better
   - Document insights' sources for traceability

2. INITIAL RESPONSE
   - Draft an initial response based on:
     * Primary customer needs
     * Applicable guidelines
     * Gathered insights
   - Focus on addressing the core request first

3. REVISION CRITERIA
   The response requires further revision if any of these conditions are met:
   - Facts or services are offered without clear sourcing from this prompt - deonted by all_facts_and_services_sourced_from_prompt being false
   - Guidelines or insights are broken (except when properly prioritized, or when broken due to insufficient data) - denoted by either `instructions_broken_due_to_missing_data` or `instructions_broken_only_due_to_prioritization`
   - The response repeats previous messages - denoted by `is_repeat_message` being true.

4. REVISION DOCUMENTATION
   Document each revision in JSON format including:
   - Complete revised message
   - Facts and sources used
   - Services offered and their sources
   - Guidelines/insights followed and broken
   - Repetition assessment
   - Prioritization decisions and rationales
   - Missing data impacts

5. COMPLETION CRITERIA
   The revision process is complete when either:
   - All guidelines and insights are satisfied, or
   - 5 revisions have been attempted, or
   - Remaining issues are justified by:
     * Explicit prioritization decisions
     * Documented data limitations
     * Customer request conflicts


PRIORITIZING INSTRUCTIONS (GUIDELINES VS. INSIGHTS)
-----------------
Deviating from an instruction (either guideline or insight) is acceptable only when the deviation arises from a deliberate prioritization, based on:
    - Conflicts with a higher-priority guideline (according to their priority scores).
    - Contradictions with a customer request.
    - Lack of sufficient context or data.
    - Conflicts with an insight (see below).
In all other cases, even if you believe that a guideline's condition does not apply, you must follow it.
If fulfilling a guideline is not possible, explicitly justify why in your response.

Guidelines vs. Insights:
Sometimes, a guideline may conflict with an insight you've derived.
For example, if your insight suggests "the customer is vegetarian," but a guideline instructs you to offer non-vegetarian dishes, prioritizing the insight would better align with the business's goals—since offering vegetarian options would clearly benefit the customer.

However, remember that the guidelines reflect the explicit wishes of the business you represent. Deviating from them should only occur if doing so does not put the business at risk.
For instance, if a guideline explicitly prohibits a specific action (e.g., "never do X"), you must not perform that action, even if requested by the customer or supported by an insight.

In cases of conflict, prioritize the business's values and ensure your decisions align with their overarching goals.

""",  # noqa
        )
        builder.add_section(
            name="message-generator-examples",
            template="""
EXAMPLES
-----------------
{formatted_shots}
""",
            props={
                "formatted_shots": self._format_shots(shots),
                "shots": shots,
            },
        )
        builder.add_section(
            name="message-generator-interaction-context",
            template="""
INTERACTION CONTEXT
-----------------
""",
            props={},
        )
        builder.add_context_variables(context_variables)
        builder.add_glossary(terms)
        builder.add_section(
            name="message-generator-guideline-descriptions",
            template=self.get_guideline_matches_text(
                ordinary_guideline_matches,
                tool_enabled_guideline_matches,
            )[0],
            props=self.get_guideline_matches_text(
                ordinary_guideline_matches,
                tool_enabled_guideline_matches,
            )[1],
            status=SectionStatus.ACTIVE
            if ordinary_guideline_matches or tool_enabled_guideline_matches
            else SectionStatus.PASSIVE,
        )
        builder.add_interaction_history(interaction_history)
        builder.add_staged_events(staged_events)

        if tool_insights.missing_data:
            builder.add_section(
                name="message-generator-missing-data-for-tools",
                template="""
MISSING DATA FOR TOOL REQUIRED CALLS:
-------------------------------------
The following is a description of missing data that has been deemed necessary
in order to run tools. The tools would have run, if they only had this data available.
If it makes sense in the current state of the interaction, you may choose to inform the user about this missing data: ###
{formatted_missing_data}
###

""",
                props={
                    "formatted_missing_data": self._format_missing_data(tool_insights.missing_data),
                    "missing_data": tool_insights.missing_data,
                },
            )

        builder.add_section(
            name="message-generator-output-format",
            template="""
OUTPUT FORMAT
-----------------

Produce a valid JSON object in the following format: ###

{default_output_format}
###
""",
            props={
                "default_output_format": self._get_output_format(
                    interaction_history,
                    list(chain(ordinary_guideline_matches, tool_enabled_guideline_matches)),
                ),
                "interaction_history": interaction_history,
                "guidelines": list(
                    chain(ordinary_guideline_matches, tool_enabled_guideline_matches)
                ),
            },
        )

        return builder

    def _format_missing_data(self, missing_data: Sequence[MissingToolData]) -> str:
        return json.dumps(
            [
                {
                    "datum_name": d.parameter,
                    **({"description": d.description} if d.description else {}),
                    **({"significance": d.significance} if d.significance else {}),
                    **({"examples": d.examples} if d.examples else {}),
                }
                for d in missing_data
            ]
        )

    def _get_output_format(
        self, interaction_history: Sequence[Event], guidelines: Sequence[GuidelineMatch]
    ) -> str:
        last_customer_message = next(
            (
                event.data["message"] if not event.data.get("flagged", False) else "<N/A>"
                for event in reversed(interaction_history)
                if (
                    event.kind == EventKind.MESSAGE
                    and event.source == EventSource.CUSTOMER
                    and isinstance(event.data, dict)
                )
            ),
            "",
        )
        guidelines_list_text = ", ".join([f'"{g.guideline}"' for g in guidelines])
        guidelines_output_format = "\n".join(
            [
                f"""
        {{
            "number": {i},
            "instruction": "{g.guideline.content.action}",
            "evaluation": "<your evaluation of how the guideline should be followed>",
            "data_available": "<explanation whether you are provided with the required data to follow this guideline now>"
        }},"""
                for i, g in enumerate(guidelines, start=1)
            ]
        )

        if len(guidelines) == 0:
            insights_output_format = """
            {
                "number": 1,
                "instruction": "<Insight #1, if it exists>",
                "evaluation": "<your evaluation of how the insight should be followed>",
                "data_available": "<explanation whether you are provided with the required data to follow this insight now>"
            },
            <Additional entries for all insights>
        """
        else:
            insights_output_format = """
            <Additional entries for all insights>
"""

        return f"""
```json
{{
    "last_message_of_customer": "{last_customer_message}",
    "produced_reply": "<BOOL, should be true unless the customer explicitly asked you not to respond>",
    "produced_reply_rationale": "<str, optional. required only if produced_reply is false>",
    "guidelines": [{guidelines_list_text}],
    "context_evaluation": {{
        "most_recent_customer_inquiries_or_needs": "<fill out accordingly>",
        "parts_of_the_context_i_have_here_if_any_with_specific_information_on_how_to_address_these_needs": "<fill out accordingly>",
        "topics_for_which_i_have_sufficient_information_and_can_therefore_help_with": "<fill out accordingly>",
        "what_i_do_not_have_enough_information_to_help_with_with_based_on_the_provided_information_that_i_have": "<fill out accordingly>",
        "was_i_given_specific_information_here_on_how_to_address_some_of_these_specific_needs": <BOOL>,
        "should_i_tell_the_customer_i_cannot_help_with_some_of_those_needs": <BOOL>
    }},
    "insights": [<Up to 3 original insights to adhere to>],
    "evaluation_for_each_instruction": [
{guidelines_output_format}
{insights_output_format}
    ],
    "revisions": [
    {{
        "revision_number": 1,
        "content": <response chosen after revision 1>,
        "factual_information_provided": [
            {{
                "fact": <str, statement of a fact in the suggested response>
                "source": <str, source of the fact - either a specific part of this prompt or something else>
                "is_source_based_in_this_prompt": <BOOL>
            }},
            ...
        ],
        "offered_services": [
            {{
                "service": <str, statement of a fact in the suggested response>
                "source": <str, source of the fact - either a specific part of this prompt or something else>
                "is_source_based_in_this_prompt": <BOOL>
            }},
            ...
        ],
        "instructions_followed": <list of guidelines and insights that were followed>,
        "instructions_broken": <list of guidelines and insights that were broken>,
        "is_repeat_message": <BOOL, indicating whether "content" is a repeat of a previous message by the agent>,
        "followed_all_instructions": <BOOL, whether all guidelines and insights followed>,
        "instructions_broken_due_to_missing_data": <BOOL, optional. Necessary only if instructions_broken_only_due_to_prioritization is true>,
        "missing_data_rationale": <STR, optional. Necessary only if instructions_broken_due_to_missing_data is true>,
        "instructions_broken_only_due_to_prioritization": <BOOL, optional. Necessary only if followed_all_instructions is true>,
        "prioritization_rationale": <STR, optional. Necessary only if instructions_broken_only_due_to_prioritization is true>
        "all_facts_and_services_sourced_from_prompt": <BOOL, if false, you must produce further revisions>,
        "further_revisions_required": <BOOL, true iff either instructions were broken due to invalid reasons, if is_repeat_message is true, or if all_facts_and_services_sourced_from_prompt is false>
    }},
    ...
    ]
}}
```
###"""

    async def _generate_response_message(
        self,
        prompt: PromptBuilder,
        temperature: float,
        final_attempt: bool,
    ) -> tuple[GenerationInfo, Optional[str]]:
        message_event_response = await self._schematic_generator.generate(
            prompt=prompt,
            hints={"temperature": temperature},
        )

        self._logger.debug(
            f"Completion:\n{message_event_response.content.model_dump_json(indent=2)}"
        )

        if (
            message_event_response.content.produced_reply is False
            or not message_event_response.content.revisions
        ):
            self._logger.debug("Produced no reply")
            return message_event_response.info, None

        if first_correct_revision := next(
            (
                r
                for r in message_event_response.content.revisions
                if not r.is_repeat_message
                and (
                    r.followed_all_instructions
                    or r.instructions_broken_only_due_to_prioritization
                    or r.instructions_broken_due_to_missing_data
                )
            ),
            None,
        ):
            # Sometimes the LLM continues generating revisions even after
            # it generated a correct one. Those next revisions tend to be
            # faulty, as they do not handle prioritization well. This is a workaround.
            final_revision = first_correct_revision
        else:
            final_revision = message_event_response.content.revisions[-1]

        if (
            not final_revision.followed_all_instructions
            and not final_revision.instructions_broken_only_due_to_prioritization
        ) or final_revision.is_repeat_message:
            if not final_attempt:
                self._logger.warning(
                    f"Trying again after problematic message generation: {final_revision.content}"
                )
                raise Exception("Retry with another attempt")
            else:
                self._logger.warning(
                    f"Conceding despite problematic message generation: {final_revision.content}"
                )

        return message_event_response.info, str(final_revision.content)


example_1_expected = MessageSchema(
    last_message_of_customer="Hi, I'd like to know the schedule for the next trains to Boston, please.",
    produced_reply=True,
    guidelines=[
        "When the customer asks for train schedules, provide them accurately and concisely."
    ],
    context_evaluation=ContextEvaluation(
        most_recent_customer_inquiries_or_needs="Knowing the schedule for the next trains to Boston",
        parts_of_the_context_i_have_here_if_any_with_specific_information_on_how_to_address_these_needs="The interaction history contains a tool call with the train schedule for Boston",
        topics_for_which_i_have_sufficient_information_and_can_therefore_help_with="I can provide the schedule directly from the tool call's result",
        what_i_do_not_have_enough_information_to_help_with_with_based_on_the_provided_information_that_i_have="I am not given the current time so I can't say what trains are *next*",
        was_i_given_specific_information_here_on_how_to_address_some_of_these_specific_needs=True,
        should_i_tell_the_customer_i_cannot_help_with_some_of_those_needs=True,
    ),
    insights=[
        "Use markdown format when applicable.",
        "Provide the train schedule without specifying which trains are *next*.",
    ],
    evaluation_for_each_instruction=[
        InstructionEvaluation(
            number=1,
            instruction="When the customer asks for train schedules, provide them accurately and concisely.",
            evaluation="The customer requested train schedules, so I need to respond with accurate timing information.",
            data_available="Yes, the train schedule data is available.",
        ),
        InstructionEvaluation(
            number=2,
            instruction="Use markdown format when applicable.",
            evaluation="Markdown formatting makes the schedule clearer and more readable.",
            data_available="Not specifically needed, but markdown format can be applied to any response.",
        ),
        InstructionEvaluation(
            number=3,
            instruction="Provide the train schedule without specifying which trains are *next*.",
            evaluation="I don't want to mislead the user so, while I can provide the schedule, I should be clear that I don't know which trains are next",
            data_available="I have the schedule itself, so I can conform to this instruction.",
        ),
    ],
    revisions=[
        Revision(
            revision_number=1,
            content=(
                "Train Schedule:\n"
                "Train 101 departs at 10:00 AM and arrives at 12:30 PM.\n"
                "Train 205 departs at 1:00 PM and arrives at 3:45 PM."
            ),
            factual_information_provided=[
                FactualInformationEvaluation(
                    fact="Train 101 departs at 10:00 AM and arrives at 12:30 PM.",
                    source="Staged event data",
                    is_source_based_in_this_prompt=True,
                ),
                FactualInformationEvaluation(
                    fact="Train 205 departs at 1:00 PM and arrives at 3:45 PM.",
                    source="Staged event data",
                    is_source_based_in_this_prompt=True,
                ),
            ],
            offered_services=[],
            instructions_followed=[
                "#1; When the customer asks for train schedules, provide them accurately and concisely."
            ],
            instructions_broken=[
                "#2; Did not use markdown format when applicable.",
                "#3; Was not clear enough that I don't know which trains are next because I don't have the time",
            ],
            is_repeat_message=False,
            followed_all_instructions=False,
            instructions_broken_due_to_missing_data=False,
            instructions_broken_only_due_to_prioritization=False,
            all_facts_and_services_sourced_from_prompt=True,
            further_revisions_required=True,
        ),
        Revision(
            revision_number=2,
            content=(
                """
                Here's the schedule for Boston, but please note that as I don't have the current time, I can't say which trains are next to arrive right now.

                | Train | Departure | Arrival |
                |-------|-----------|---------|
                | 101   | 10:00 AM  | 12:30 PM |
                | 205   | 1:00 PM   | 3:45 PM  |"""
            ),
            factual_information_provided=[
                FactualInformationEvaluation(
                    fact="Train 101 departs at 10:00 AM and arrives at 12:30 PM.",
                    source="Staged event data",
                    is_source_based_in_this_prompt=True,
                ),
                FactualInformationEvaluation(
                    fact="Train 205 departs at 1:00 PM and arrives at 3:45 PM.",
                    source="Staged event data",
                    is_source_based_in_this_prompt=True,
                ),
            ],
            offered_services=[],
            instructions_followed=[
                "#1; When the customer asks for train schedules, provide them accurately and concisely.",
                "#2; Use markdown format when applicable.",
                "#3; Clearly stated that I can't guarantee which trains are next as I don't have the time.",
            ],
            instructions_broken=[],
            is_repeat_message=False,
            followed_all_instructions=True,
            all_facts_and_services_sourced_from_prompt=True,
            further_revisions_required=False,
        ),
    ],
)

example_1_shot = MessageGeneratorShot(
    description="A reply that took critique in a few revisions to get right",
    expected_result=example_1_expected,
)


example_2_expected = MessageSchema(
    last_message_of_customer="Alright, can I get the American burger with cheese?",
    guidelines=[
        "When the customer chooses and orders a burger, then provide it",
        "When the customer chooses specific ingredients on the burger, only provide those ingredients if we have them fresh in stock; otherwise, reject the order",
    ],
    context_evaluation=ContextEvaluation(
        most_recent_customer_inquiries_or_needs="The customer ordered an American burger with cheese",
        parts_of_the_context_i_have_here_if_any_with_specific_information_on_how_to_address_these_needs="Our cheese has expired",
        was_i_given_specific_information_here_on_how_to_address_some_of_these_specific_needs=True,
        should_i_tell_the_customer_i_cannot_help_with_some_of_those_needs=True,
        topics_for_which_i_have_sufficient_information_and_can_therefore_help_with="",
        what_i_do_not_have_enough_information_to_help_with_with_based_on_the_provided_information_that_i_have=None,
    ),
    insights=[],
    evaluation_for_each_instruction=[
        InstructionEvaluation(
            number=1,
            instruction="When the customer chooses and orders a burger, then provide it",
            evaluation="This guideline currently applies, so I need to provide the customer with a burger.",
            data_available="The burger choice is available in the interaction",
        ),
        InstructionEvaluation(
            number=2,
            instruction="When the customer chooses specific ingredients on the burger, only provide those ingredients if we have them fresh in stock; otherwise, reject the order.",
            evaluation="The customer chose cheese on the burger, but all of the cheese we currently have is expired",
            data_available="The relevant stock availability is given in the tool calls' data. Our cheese has expired.",
        ),
    ],
    revisions=[
        Revision(
            revision_number=1,
            content=(
                "I'd be happy to prepare your burger as soon as we restock the requested toppings."
            ),
            factual_information_provided=[
                FactualInformationEvaluation(
                    fact="The topping the customer requested (cheese) is out of stock",
                    source="Staged event data",
                    is_source_based_in_this_prompt=True,
                ),
            ],
            offered_services=[
                OfferedServiceEvaluation(
                    service="preparing burgers",
                    source="guideline to provide burgers to the customer",
                    is_source_based_in_this_prompt=True,
                ),
            ],
            instructions_followed=[
                "#2; upheld food quality and did not go on to preparing the burger without fresh toppings."
            ],
            instructions_broken=[
                "#1; did not provide the burger with requested toppings immediately due to the unavailability of fresh ingredients."
            ],
            is_repeat_message=False,
            followed_all_instructions=False,
            instructions_broken_only_due_to_prioritization=True,
            prioritization_rationale=(
                "Given the higher priority score of guideline 2, maintaining food quality "
                "standards before serving the burger is prioritized over immediate service."
            ),
            instructions_broken_due_to_missing_data=False,
            all_facts_and_services_sourced_from_prompt=True,
            further_revisions_required=False,
        )
    ],
)

example_2_shot = MessageGeneratorShot(
    description="A reply where one instruction was prioritized over another",
    expected_result=example_2_expected,
)


example_3_expected = MessageSchema(
    last_message_of_customer="Hi there, can I get something to drink? What do you have on tap?",
    guidelines=["When the customer asks for a drink, check the menu and offer what's on it"],
    context_evaluation=ContextEvaluation(
        most_recent_customer_inquiries_or_needs="Knowing what drinks we have on tap",
        parts_of_the_context_i_have_here_if_any_with_specific_information_on_how_to_address_these_needs="None",
        was_i_given_specific_information_here_on_how_to_address_some_of_these_specific_needs=False,
        should_i_tell_the_customer_i_cannot_help_with_some_of_those_needs=True,
        topics_for_which_i_have_sufficient_information_and_can_therefore_help_with=None,
        what_i_do_not_have_enough_information_to_help_with_with_based_on_the_provided_information_that_i_have="I was not given any contextual information (including tool calls) about what drinks we have at all",
    ),
    insights=[
        "Do not state factual information that you do not know, don't have access to, or are not sure about."
    ],
    evaluation_for_each_instruction=[
        InstructionEvaluation(
            number=1,
            instruction="When the customer asks for a drink, check the menu and offer what's on it",
            evaluation="The customer did ask for a drink, so I should check the menu to see what's available.",
            data_available="No, I don't have the menu info in the interaction or tool calls",
        ),
        InstructionEvaluation(
            number=2,
            instruction="Do not state factual information that you do not know or are not sure about",
            evaluation="There's no information about what we have on tap, so I should not offer any specific option.",
            data_available="No, the list of available drinks is not available to me",
        ),
    ],
    revisions=[
        Revision(
            revision_number=1,
            content=(
                "I'm sorry, but I'm having trouble accessing our menu at the moment. Can I help you with anything else in the meanwhile?"
            ),
            factual_information_provided=[
                FactualInformationEvaluation(
                    fact="I'm having trouble accessing our menu",
                    source="no menu details listed in the prompt",
                    is_source_based_in_this_prompt=True,
                ),
            ],
            offered_services=[],
            instructions_followed=[
                "#2; Do not state factual information that you do not know or are not sure about"
            ],
            instructions_broken=[
                "#1; Lacking menu data in the context prevented me from providing the client with drink information."
            ],
            is_repeat_message=False,
            followed_all_instructions=False,
            missing_data_rationale="Menu data was missing",
            instructions_broken_due_to_missing_data=True,
            instructions_broken_only_due_to_prioritization=False,
            all_facts_and_services_sourced_from_prompt=True,
            further_revisions_required=False,
        )
    ],
)

example_3_shot = MessageGeneratorShot(
    description="Non-Adherence Due to Missing Data. Assume the menu isn't listed anywhere in the prompt",
    expected_result=example_3_expected,
)


example_4_expected = MessageSchema(
    last_message_of_customer="This is not what I was asking for",
    guidelines=[],
    context_evaluation=ContextEvaluation(
        most_recent_customer_inquiries_or_needs="At this point it appears that I do not understand what the customer is asking",
    ),
    insights=["I should not keep repeating myself as it makes me sound robotic"],
    evaluation_for_each_instruction=[
        InstructionEvaluation(
            number=1,
            instruction="I should not keep repeating myself as it makes me sound robotic",
            evaluation="If I keep repeating myself in asking for clarifications, it makes me sound robotic and unempathetic as if I'm not really tuned into the customer's vibe",
            data_available="None needed",
        )
    ],
    revisions=[
        Revision(
            revision_number=1,
            content="I apologize for the confusion. Could you please explain what I'm missing?",
            factual_information_provided=[],
            offered_services=[],
            instructions_followed=[],
            instructions_broken=[
                "#1; I've already apologized and asked for clarifications, and I shouldn't repeat myself"
            ],
            is_repeat_message=True,
            followed_all_instructions=False,
            all_facts_and_services_sourced_from_prompt=True,
            further_revisions_required=True,
        ),
        Revision(
            revision_number=2,
            content="I see. What am I missing?",
            factual_information_provided=[],
            offered_services=[],
            instructions_followed=[],
            instructions_broken=[
                "#1; Asking what I'm missing is still asking for clarifications, and I shouldn't repeat myself"
            ],
            is_repeat_message=True,
            followed_all_instructions=False,
            all_facts_and_services_sourced_from_prompt=True,
            further_revisions_required=True,
        ),
        Revision(
            revision_number=3,
            content=(
                "It seems like I'm failing to assist you with your issue. "
                "Let me know if there's anything else I can do for you."
            ),
            factual_information_provided=[],
            offered_services=[],
            instructions_followed=[
                "#1; I broke of out of the self-repeating loop by admitting that I can't seem to help"
            ],
            instructions_broken=[],
            is_repeat_message=False,
            followed_all_instructions=True,
            all_facts_and_services_sourced_from_prompt=True,
            further_revisions_required=False,
        ),
    ],
)

example_4_shot = MessageGeneratorShot(
    description="Avoiding repetitive responses—in this case, given that the previous response by the agent was 'I am sorry, could you please clarify your request?'",
    expected_result=example_4_expected,
)


example_5_expected = MessageSchema(
    last_message_of_customer=(
        "How much money do I have in my account, and how do you know it? Is there some service you use to check "
        "my balance? Can I access it too?"
    ),
    guidelines=["When you need the balance of a customer, then use the 'check_balance' tool."],
    context_evaluation=ContextEvaluation(
        most_recent_customer_inquiries_or_needs="Know how much money they have in their account; Knowing how and what I use to know how much money they have",
        parts_of_the_context_i_have_here_if_any_with_specific_information_on_how_to_address_these_needs="I know how much money they have based on a tool call's result",
        was_i_given_specific_information_here_on_how_to_address_some_of_these_specific_needs=True,
        should_i_tell_the_customer_i_cannot_help_with_some_of_those_needs=False,
        topics_for_which_i_have_sufficient_information_and_can_therefore_help_with="Telling them how much is in their account",
        what_i_do_not_have_enough_information_to_help_with_with_based_on_the_provided_information_that_i_have="I should not expose my internal process, despite their request",
    ),
    insights=["Never reveal details about the process you followed to produce your response"],
    evaluation_for_each_instruction=[
        InstructionEvaluation(
            number=1,
            instruction="use the 'check_balance' tool",
            evaluation="There's already a staged tool call with this tool, so no further action is required.",
            data_available="Yes, I know that the customer's balance is 1,000$",
        ),
        InstructionEvaluation(
            number=1,
            instruction="Never reveal details about the process you followed to produce your response",
            evaluation="The reply must not reveal details about how I know the client's balance",
            data_available="Not needed",
        ),
    ],
    revisions=[
        Revision(
            revision_number=1,
            content=(
                "Your balance is $1,000. As a helpful assistant, I have the resources necessary to provide "
                "accurate information. However, I’m unable to disclose details about the specific services I use. "
                "Is there anything else I can assist you with?"
            ),
            factual_information_provided=[
                FactualInformationEvaluation(
                    fact="The customer's balance is $1,000",
                    source="tool call result",
                    is_source_based_in_this_prompt=True,
                )
            ],
            offered_services=[],
            instructions_followed=[
                "#1; use the 'check_balance' tool",
                "#2; Never reveal details about the process you followed to produce your response",
            ],
            instructions_broken=[],
            is_repeat_message=False,
            followed_all_instructions=True,
            all_facts_and_services_sourced_from_prompt=True,
            further_revisions_required=False,
        )
    ],
)

example_5_shot = MessageGeneratorShot(
    description="Not exposing thought process: Assume a tool call for 'check_balance' with a returned value of 1,000$ is staged",
    expected_result=example_5_expected,
)


example_6_expected = MessageSchema(
    last_message_of_customer=(
        "Alright I have the documents ready, how can I send them to you guys?"
    ),
    guidelines=[],
    insights=[],
    evaluation_for_each_instruction=[
        InstructionEvaluation(
            number=1,
            instruction="ONLY OFFER SERVICES AND INFORMATION PROVIDED IN THIS PROMPT",
            evaluation="I must not output any contact information, since it was not provided within this prompt.",
            data_available="Contact info is not available",
        ),
    ],
    revisions=[
        Revision(
            revision_number=1,
            content=(
                "Thank you for reaching out! To ensure your documents are handled securely, please follow these steps:"
                "Email your <NAME_EMAIL>."
                "If your materials are sensitive or require encryption, let us know so we can provide additional instructions."
            ),
            factual_information_provided=[],
            offered_services=[
                OfferedServiceEvaluation(
                    service="We receive <NAME_EMAIL>",
                    source="General knowledge about the public engagement office",
                    is_source_based_in_this_prompt=False,
                ),
                OfferedServiceEvaluation(
                    service="Additional instructions can be provided if sensitive materials need to be shipped to us",
                    source="Assumption about proper procedure",
                    is_source_based_in_this_prompt=False,
                ),
            ],
            instructions_followed=[],
            instructions_broken=["#1; ONLY OFFER SERVICES AND INFORMATION PROVIDED IN THIS PROMPT"],
            is_repeat_message=False,
            followed_all_instructions=False,
            all_facts_and_services_sourced_from_prompt=False,
            further_revisions_required=True,
        ),
        Revision(
            revision_number=2,
            content=(
                "Thank you for reaching out! Unfortunately I don’t have the specific contact information for the Department of Public Engagement. I’d suggest checking online or reaching out to your local representative—they should be able to help!"
            ),
            factual_information_provided=[],
            offered_services=[],
            instructions_followed=[
                "#1; ONLY OFFER SERVICES AND INFORMATION PROVIDED IN THIS PROMPT"
            ],
            instructions_broken=[],
            is_repeat_message=False,
            followed_all_instructions=False,
            instructions_broken_due_to_missing_data=False,
            instructions_broken_only_due_to_prioritization=False,
            all_facts_and_services_sourced_from_prompt=True,
            further_revisions_required=False,
        ),
    ],
)

example_6_shot = MessageGeneratorShot(
    description="Not providing information outside of what's provided in the prompt: Assume the agent works for the white house's office of public engagement. Assume no contact information was given as part of the prompt.",
    expected_result=example_6_expected,
)

example_7_expected = MessageSchema(
    last_message_of_customer=("Hey, how can I contact customer support?"),
    guidelines=[],
    context_evaluation=ContextEvaluation(
        most_recent_customer_inquiries_or_needs="The customer wants to know how to contact customer support",
        parts_of_the_context_i_have_here_if_any_with_specific_information_on_how_to_address_these_needs="The system has given me no information on contacting customer support",
        topics_for_which_i_have_sufficient_information_and_can_therefore_help_with="None in this case; I'm not authorized to offer help beyond my configured capabilities",
        what_i_do_not_have_enough_information_to_help_with_with_based_on_the_provided_information_that_i_have="I cannot help with contacting customer support",
        was_i_given_specific_information_here_on_how_to_address_some_of_these_specific_needs=False,
        should_i_tell_the_customer_i_cannot_help_with_some_of_those_needs=True,
    ),
    insights=["When I cannot help with a topic, I should tell the customer I can't help with it"],
    evaluation_for_each_instruction=[
        InstructionEvaluation(
            number=1,
            instruction="When I cannot help with a topic, I should tell the customer I can't help with it",
            evaluation="Indeed, no information on contacting customer support is provided in my context",
            data_available="Not needed",
        ),
    ],
    revisions=[
        Revision(
            revision_number=1,
            content=(
                "Could you please provide more details on what you would need from customer support? Maybe I could help you."
            ),
            factual_information_provided=[],
            offered_services=[],
            instructions_followed=[],
            instructions_broken=[
                "#1; Instead of saying I can't help, I asked for more details from the customer",
            ],
            is_repeat_message=False,
            followed_all_instructions=False,
            all_facts_and_services_sourced_from_prompt=True,
            further_revisions_required=True,
        ),
        Revision(
            revision_number=2,
            content=(
                "Unfortunately I cannot help you with this topic as I do not have enough information on it. Is there anything else I can assist you with?"
            ),
            factual_information_provided=[],
            offered_services=[],
            instructions_followed=[
                "#1; I adhered to the instruction by clearly stating that I cannot help with this topic",
            ],
            instructions_broken=[],
            is_repeat_message=False,
            followed_all_instructions=True,
            all_facts_and_services_sourced_from_prompt=True,
            further_revisions_required=False,
        ),
    ],
)

example_7_shot = MessageGeneratorShot(
    description="An insight is derived and followed on not offering to help with something you don't know about",
    expected_result=example_7_expected,
)


example_8_expected = MessageSchema(
    last_message_of_customer="I don't have any android devices, and I do not want to buy a ticket at the moment. Now, what flights are there from New York to Los Angeles tomorrow?",
    guidelines=[
        "When asked anything about plane tickets, suggest completing the order on our android app",
        "When asked about first-class tickets, mention that shorter flights do not offer a complementary meal",
    ],
    context_evaluation=ContextEvaluation(
        most_recent_customer_inquiries_or_needs="Knowing what flights there are from NY to LA tomorrow",
        parts_of_the_context_i_have_here_if_any_with_specific_information_on_how_to_address_these_needs="Today's date is [...] and I can see the relevant flight schedule in a staged tool call",
        was_i_given_specific_information_here_on_how_to_address_some_of_these_specific_needs=True,
        should_i_tell_the_customer_i_cannot_help_with_some_of_those_needs=False,
        topics_for_which_i_have_sufficient_information_and_can_therefore_help_with="I know the date today, and I have the relevant flight schedule",
        what_i_do_not_have_enough_information_to_help_with_with_based_on_the_provided_information_that_i_have=None,
    ),
    insights=[
        "In your generated reply to the customer, use markdown format when applicable.",
        "The customer does not have an android device and does not want to buy anything",
    ],
    evaluation_for_each_instruction=[
        InstructionEvaluation(
            number=1,
            instruction="When asked anything about plane tickets, suggest completing the order on our android app",
            evaluation="I should suggest completing the order on our android app",
            data_available="Yes, I know that the name of our android app is BestPlaneTickets",
        ),
        InstructionEvaluation(
            number=2,
            instruction="When asked about first-class tickets, mention that shorter flights do not offer a complementary meal",
            evaluation="Evaluating whether the 'when' condition applied is not my role. I should therefore just mention that shorter flights do not offer a complementary meal",
            data_available="not needed",
        ),
        InstructionEvaluation(
            number=3,
            instruction="In your generated reply to the customer, use markdown format when applicable",
            evaluation="I need to output a message in markdown format",
            data_available="Not needed",
        ),
        InstructionEvaluation(
            number=4,
            instruction="The customer does not have an android device and does not want to buy anything",
            evaluation="A guideline should not override a customer's request, so I should not suggest products requiring an android device",
            data_available="Not needed",
        ),
    ],
    revisions=[
        Revision(
            revision_number=1,
            content=(
                """
                | Option | Departure Airport | Departure Time | Arrival Airport   |
                |--------|-------------------|----------------|-------------------|
                | 1      | Newark (EWR)      | 10:00 AM       | Los Angeles (LAX) |
                | 2      | JFK               | 3:30 PM        | Los Angeles (LAX) |
                While these flights are quite long, please note that we do not offer complementary meals on short flights."""
            ),
            factual_information_provided=[
                FactualInformationEvaluation(
                    fact="A flight from EWR to LAX departs at 10:00 AM",
                    source="tool call result",
                    is_source_based_in_this_prompt=True,
                ),
                FactualInformationEvaluation(
                    fact="A flight from JFK to LAX departs at 3:30 PM",
                    source="tool call result",
                    is_source_based_in_this_prompt=True,
                ),
            ],
            offered_services=[],
            instructions_followed=[
                "#2; When asked about first-class tickets, mention that shorter flights do not offer a complementary meal",
                "#3; In your generated reply to the customer, use markdown format when applicable.",
                "#4; The customer does not have an android device and does not want to buy anything",
            ],
            instructions_broken=[
                "#1; When asked anything about plane tickets, suggest completing the order on our android app."
            ],
            is_repeat_message=False,
            followed_all_instructions=False,
            instructions_broken_only_due_to_prioritization=True,
            prioritization_rationale=(
                "Instructions #1 and #3 contradict each other, and customer requests take precedent "
                "over guidelines, so instruction #1 was prioritized."
            ),
            instructions_broken_due_to_missing_data=False,
            all_facts_and_services_sourced_from_prompt=True,
            further_revisions_required=False,
        )
    ],
)

example_8_shot = MessageGeneratorShot(
    description="Applying Insight—assuming the agent is provided with a list of outgoing flights from a tool call",
    expected_result=example_8_expected,
)

example_9_expected = MessageSchema(
    last_message_of_customer=("You are not being helpful. Transfer me to a human."),
    guidelines=[],
    context_evaluation=ContextEvaluation(
        most_recent_customer_inquiries_or_needs="The customer wants to be transfered to a human",
        parts_of_the_context_i_have_here_if_any_with_specific_information_on_how_to_address_these_needs="The system has given me no information on how to transfer a customer to a human representitive",
        topics_for_which_i_have_sufficient_information_and_can_therefore_help_with="None, I can only apologize and explain myself",
        what_i_do_not_have_enough_information_to_help_with_with_based_on_the_provided_information_that_i_have="I lack information on how to transfer the customer to a human representitive",
        was_i_given_specific_information_here_on_how_to_address_some_of_these_specific_needs=False,
        should_i_tell_the_customer_i_cannot_help_with_some_of_those_needs=True,
    ),
    insights=[
        "The customer is dissatisfied with my service, meaning I should apologize and attempt to assist them with their issue"
    ],
    evaluation_for_each_instruction=[
        InstructionEvaluation(
            number=1,
            instruction="The customer is dissatisfied with my service, meaning I should apologize and attempt to assist them with their issue",
            evaluation="I should apologize and attempt to medigate the issue",
            data_available="Not needed",
        ),
    ],
    revisions=[
        Revision(
            revision_number=1,
            content=(
                "I apologize for the inconvenience. Unfortunately I cannot transfer you to a human representitive at this moment. I'd recommend calling us by phone, hopefuly they'd be able to help you there."
            ),
            factual_information_provided=[],
            offered_services=[
                OfferedServiceEvaluation(
                    service="suggested calling our store by phone",
                    source="General knowledge regarding electronic stores",
                    is_source_based_in_this_prompt=False,
                )
            ],
            instructions_followed=[
                "#1; The customer is dissatisfied with my service, meaning I should apologize and attempt to assist them with their issue"
            ],
            instructions_broken=[],
            is_repeat_message=False,
            followed_all_instructions=True,
            all_facts_and_services_sourced_from_prompt=False,
            further_revisions_required=True,
        ),
        Revision(
            revision_number=2,
            content=(
                "I apologize for the inconvenience. Unfortunately I cannot transfer you to a human representitive at this moment. I recommend visiting one of our branches, and getting help from a human representative there"
            ),
            factual_information_provided=[],
            offered_services=[
                OfferedServiceEvaluation(
                    service="suggested visiting one of our branches",
                    source="General knowledge regarding electronic stores",
                    is_source_based_in_this_prompt=False,
                )
            ],
            instructions_followed=[
                "#1; The customer is dissatisfied with my service, meaning I should apologize and attempt to assist them with their issue"
            ],
            instructions_broken=[],
            is_repeat_message=False,
            followed_all_instructions=True,
            all_facts_and_services_sourced_from_prompt=False,
            further_revisions_required=True,
        ),
        Revision(
            revision_number=2,
            content=(
                "I'm really sorry I couldn’t provide the help you needed. Unfortunately, I don’t have the option to transfer you to a human representative. If there’s anything else I can try to assist with, feel free to let me know."
            ),
            factual_information_provided=[],
            offered_services=[
                OfferedServiceEvaluation(
                    service="general assistance",
                    source="the description of my role",
                    is_source_based_in_this_prompt=True,
                )
            ],
            instructions_followed=[],
            instructions_broken=[
                "#1; The customer is dissatisfied with my service, meaning I should apologize and attempt to assist them with their issue"
            ],
            is_repeat_message=False,
            followed_all_instructions=False,
            instructions_broken_due_to_missing_data=True,
            missing_data_rationale="I lack information about how to transfer the customer to a human representative",
            all_facts_and_services_sourced_from_prompt=True,
            further_revisions_required=False,
        ),
    ],
)

example_9_shot = MessageGeneratorShot(
    description="Handling a frustrated customer when no options for assistance are available to the agent. Assume the agent works for a large electronic store, and that its role (as described in its prompt) is to assist potential customers. Assume the prompt did not specify a method for transfering customers to human representatives",
    expected_result=example_7_expected,
)

_baseline_shots: Sequence[MessageGeneratorShot] = [
    example_1_shot,
    example_2_shot,
    example_3_shot,
    example_4_shot,
    example_5_shot,
    example_6_shot,
    example_7_shot,
    example_8_shot,
    example_9_shot,
]

shot_collection = ShotCollection[MessageGeneratorShot](_baseline_shots)
