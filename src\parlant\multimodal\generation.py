"""
Multi-modal content generation for Parlant.

This module provides functionality for generating multi-modal content.
"""

from __future__ import annotations
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple, Union
import uuid
import json
import base64
import io
import os
from pathlib import Path
import tempfile

from parlant.core.common import JSONSerializable, generate_id
from parlant.core.loggers import Logger
from parlant.core.async_utils import Reader<PERSON>riterLock
from parlant.core.nlp.service import NLPService
from parlant.core.nlp.generation_info import GenerationInfo

from parlant.multimodal.image import Image, ImageId, ImageProcessor, ImageMetadata, ImageFormat
from parlant.multimodal.audio import Audio, AudioId, AudioProcessor, AudioMetadata, AudioFormat
from parlant.multimodal.video import Video, VideoId, VideoProcessor, VideoMetadata, VideoFormat


class GenerationMode(str, Enum):
    """Modes for multi-modal content generation."""
    
    TEXT_TO_IMAGE = "text_to_image"
    TEXT_TO_AUDIO = "text_to_audio"
    TEXT_TO_VIDEO = "text_to_video"
    IMAGE_VARIATION = "image_variation"
    IMAGE_EDIT = "image_edit"
    AUDIO_VARIATION = "audio_variation"
    AUDIO_EDIT = "audio_edit"


@dataclass
class GenerationOptions:
    """Options for multi-modal content generation."""
    
    mode: GenerationMode
    prompt: str
    width: Optional[int] = None
    height: Optional[int] = None
    duration: Optional[float] = None
    format: Optional[str] = None
    style: Optional[str] = None
    seed: Optional[int] = None
    metadata: Dict[str, JSONSerializable] = field(default_factory=dict)


@dataclass
class GenerationResult:
    """Result of multi-modal content generation."""
    
    mode: GenerationMode
    prompt: str
    content_type: str  # "image", "audio", or "video"
    content_id: str  # ImageId, AudioId, or VideoId
    metadata: Dict[str, JSONSerializable] = field(default_factory=dict)


class MultiModalGenerator:
    """Generator for multi-modal content."""
    
    def __init__(
        self,
        nlp_service: NLPService,
        image_processor: ImageProcessor,
        audio_processor: AudioProcessor,
        video_processor: VideoProcessor,
        logger: Logger,
    ):
        """Initialize the multi-modal generator.
        
        Args:
            nlp_service: NLP service for content generation
            image_processor: Image processor
            audio_processor: Audio processor
            video_processor: Video processor
            logger: Logger instance
        """
        self._nlp_service = nlp_service
        self._image_processor = image_processor
        self._audio_processor = audio_processor
        self._video_processor = video_processor
        self._logger = logger
        self._lock = ReaderWriterLock()
        
    async def generate_content(
        self,
        options: GenerationOptions,
    ) -> GenerationResult:
        """Generate multi-modal content.
        
        Args:
            options: Generation options
            
        Returns:
            Generation result
        """
        if options.mode == GenerationMode.TEXT_TO_IMAGE:
            return await self._generate_image(options)
        elif options.mode == GenerationMode.TEXT_TO_AUDIO:
            return await self._generate_audio(options)
        elif options.mode == GenerationMode.TEXT_TO_VIDEO:
            return await self._generate_video(options)
        elif options.mode == GenerationMode.IMAGE_VARIATION:
            return await self._generate_image_variation(options)
        elif options.mode == GenerationMode.IMAGE_EDIT:
            return await self._generate_image_edit(options)
        elif options.mode == GenerationMode.AUDIO_VARIATION:
            return await self._generate_audio_variation(options)
        elif options.mode == GenerationMode.AUDIO_EDIT:
            return await self._generate_audio_edit(options)
        else:
            raise ValueError(f"Unsupported generation mode: {options.mode}")
            
    async def _generate_image(
        self,
        options: GenerationOptions,
    ) -> GenerationResult:
        """Generate an image from text.
        
        Args:
            options: Generation options
            
        Returns:
            Generation result
        """
        try:
            # Import libraries only when needed
            import requests
            from PIL import Image as PILImage
            
            # Set default values
            width = options.width or 1024
            height = options.height or 1024
            format_str = options.format or "png"
            
            # Create a prompt for image generation
            prompt = self._create_image_generation_prompt(options)
            
            # Generate image using an external API
            # Note: This is a placeholder. In a real implementation, you would use a specific image generation API.
            api_url = "https://api.example.com/generate-image"
            response = requests.post(
                api_url,
                json={
                    "prompt": prompt,
                    "width": width,
                    "height": height,
                    "format": format_str,
                    "style": options.style,
                    "seed": options.seed,
                },
            )
            
            # Check if the request was successful
            if response.status_code != 200:
                raise Exception(f"Failed to generate image: {response.text}")
                
            # Get the image data
            image_data = response.content
            
            # Create an image
            image = await self._image_processor.load_image_from_bytes(image_data, format_str)
            
            self._logger.info(f"Generated image: {image.id}")
            
            # Create generation result
            result = GenerationResult(
                mode=options.mode,
                prompt=options.prompt,
                content_type="image",
                content_id=image.id,
                metadata={
                    "width": image.metadata.width,
                    "height": image.metadata.height,
                    "format": image.metadata.format.value,
                    "style": options.style,
                    "seed": options.seed,
                },
            )
            
            return result
            
        except Exception as e:
            self._logger.error(f"Error generating image: {e}")
            
            # Create a placeholder image
            image = await self._create_placeholder_image(width, height, format_str)
            
            # Create generation result
            result = GenerationResult(
                mode=options.mode,
                prompt=options.prompt,
                content_type="image",
                content_id=image.id,
                metadata={
                    "width": image.metadata.width,
                    "height": image.metadata.height,
                    "format": image.metadata.format.value,
                    "error": str(e),
                },
            )
            
            return result
            
    async def _generate_audio(
        self,
        options: GenerationOptions,
    ) -> GenerationResult:
        """Generate audio from text.
        
        Args:
            options: Generation options
            
        Returns:
            Generation result
        """
        try:
            # Import libraries only when needed
            import requests
            
            # Set default values
            duration = options.duration or 10.0
            format_str = options.format or "mp3"
            
            # Create a prompt for audio generation
            prompt = self._create_audio_generation_prompt(options)
            
            # Generate audio using an external API
            # Note: This is a placeholder. In a real implementation, you would use a specific audio generation API.
            api_url = "https://api.example.com/generate-audio"
            response = requests.post(
                api_url,
                json={
                    "prompt": prompt,
                    "duration": duration,
                    "format": format_str,
                    "style": options.style,
                    "seed": options.seed,
                },
            )
            
            # Check if the request was successful
            if response.status_code != 200:
                raise Exception(f"Failed to generate audio: {response.text}")
                
            # Get the audio data
            audio_data = response.content
            
            # Create audio
            audio = await self._audio_processor.load_audio_from_bytes(audio_data, format_str)
            
            self._logger.info(f"Generated audio: {audio.id}")
            
            # Create generation result
            result = GenerationResult(
                mode=options.mode,
                prompt=options.prompt,
                content_type="audio",
                content_id=audio.id,
                metadata={
                    "duration": audio.metadata.duration_seconds,
                    "format": audio.metadata.format.value,
                    "style": options.style,
                    "seed": options.seed,
                },
            )
            
            return result
            
        except Exception as e:
            self._logger.error(f"Error generating audio: {e}")
            
            # Create a placeholder audio
            audio = await self._create_placeholder_audio(duration, format_str)
            
            # Create generation result
            result = GenerationResult(
                mode=options.mode,
                prompt=options.prompt,
                content_type="audio",
                content_id=audio.id,
                metadata={
                    "duration": audio.metadata.duration_seconds,
                    "format": audio.metadata.format.value,
                    "error": str(e),
                },
            )
            
            return result
            
    async def _generate_video(
        self,
        options: GenerationOptions,
    ) -> GenerationResult:
        """Generate a video from text.
        
        Args:
            options: Generation options
            
        Returns:
            Generation result
        """
        try:
            # Import libraries only when needed
            import requests
            
            # Set default values
            width = options.width or 1280
            height = options.height or 720
            duration = options.duration or 10.0
            format_str = options.format or "mp4"
            
            # Create a prompt for video generation
            prompt = self._create_video_generation_prompt(options)
            
            # Generate video using an external API
            # Note: This is a placeholder. In a real implementation, you would use a specific video generation API.
            api_url = "https://api.example.com/generate-video"
            response = requests.post(
                api_url,
                json={
                    "prompt": prompt,
                    "width": width,
                    "height": height,
                    "duration": duration,
                    "format": format_str,
                    "style": options.style,
                    "seed": options.seed,
                },
            )
            
            # Check if the request was successful
            if response.status_code != 200:
                raise Exception(f"Failed to generate video: {response.text}")
                
            # Get the video data
            video_data = response.content
            
            # Create a temporary file
            with tempfile.NamedTemporaryFile(suffix=f".{format_str}", delete=False) as temp_file:
                temp_file.write(video_data)
                temp_path = temp_file.name
                
            try:
                # Load the video
                video = await self._video_processor.load_video(temp_path)
                
                self._logger.info(f"Generated video: {video.id}")
                
                # Create generation result
                result = GenerationResult(
                    mode=options.mode,
                    prompt=options.prompt,
                    content_type="video",
                    content_id=video.id,
                    metadata={
                        "width": video.metadata.width,
                        "height": video.metadata.height,
                        "duration": video.metadata.duration_seconds,
                        "format": video.metadata.format.value,
                        "style": options.style,
                        "seed": options.seed,
                    },
                )
                
                return result
                
            finally:
                # Remove the temporary file
                os.unlink(temp_path)
                
        except Exception as e:
            self._logger.error(f"Error generating video: {e}")
            
            # Create a placeholder video
            video = await self._create_placeholder_video(width, height, duration, format_str)
            
            # Create generation result
            result = GenerationResult(
                mode=options.mode,
                prompt=options.prompt,
                content_type="video",
                content_id=video.id,
                metadata={
                    "width": video.metadata.width,
                    "height": video.metadata.height,
                    "duration": video.metadata.duration_seconds,
                    "format": video.metadata.format.value,
                    "error": str(e),
                },
            )
            
            return result
            
    async def _generate_image_variation(
        self,
        options: GenerationOptions,
    ) -> GenerationResult:
        """Generate a variation of an image.
        
        Args:
            options: Generation options
            
        Returns:
            Generation result
        """
        # TODO: Implement image variation generation
        return await self._generate_image(options)
        
    async def _generate_image_edit(
        self,
        options: GenerationOptions,
    ) -> GenerationResult:
        """Generate an edited image.
        
        Args:
            options: Generation options
            
        Returns:
            Generation result
        """
        # TODO: Implement image edit generation
        return await self._generate_image(options)
        
    async def _generate_audio_variation(
        self,
        options: GenerationOptions,
    ) -> GenerationResult:
        """Generate a variation of audio.
        
        Args:
            options: Generation options
            
        Returns:
            Generation result
        """
        # TODO: Implement audio variation generation
        return await self._generate_audio(options)
        
    async def _generate_audio_edit(
        self,
        options: GenerationOptions,
    ) -> GenerationResult:
        """Generate edited audio.
        
        Args:
            options: Generation options
            
        Returns:
            Generation result
        """
        # TODO: Implement audio edit generation
        return await self._generate_audio(options)
        
    def _create_image_generation_prompt(
        self,
        options: GenerationOptions,
    ) -> str:
        """Create a prompt for image generation.
        
        Args:
            options: Generation options
            
        Returns:
            Prompt for image generation
        """
        prompt = options.prompt
        
        if options.style:
            prompt += f", style: {options.style}"
            
        return prompt
        
    def _create_audio_generation_prompt(
        self,
        options: GenerationOptions,
    ) -> str:
        """Create a prompt for audio generation.
        
        Args:
            options: Generation options
            
        Returns:
            Prompt for audio generation
        """
        prompt = options.prompt
        
        if options.style:
            prompt += f", style: {options.style}"
            
        return prompt
        
    def _create_video_generation_prompt(
        self,
        options: GenerationOptions,
    ) -> str:
        """Create a prompt for video generation.
        
        Args:
            options: Generation options
            
        Returns:
            Prompt for video generation
        """
        prompt = options.prompt
        
        if options.style:
            prompt += f", style: {options.style}"
            
        return prompt
        
    async def _create_placeholder_image(
        self,
        width: int,
        height: int,
        format_str: str,
    ) -> Image:
        """Create a placeholder image.
        
        Args:
            width: Image width
            height: Image height
            format_str: Image format
            
        Returns:
            Placeholder image
        """
        # Import libraries only when needed
        from PIL import Image as PILImage, ImageDraw, ImageFont
        
        # Create a blank image
        pil_image = PILImage.new("RGB", (width, height), color=(200, 200, 200))
        
        # Add text
        draw = ImageDraw.Draw(pil_image)
        text = "Placeholder Image"
        
        # Calculate text position
        text_width, text_height = draw.textsize(text)
        position = ((width - text_width) // 2, (height - text_height) // 2)
        
        # Draw text
        draw.text(position, text, fill=(0, 0, 0))
        
        # Convert to bytes
        img_byte_arr = io.BytesIO()
        pil_image.save(img_byte_arr, format=format_str.upper())
        img_bytes = img_byte_arr.getvalue()
        
        # Create image
        image = Image(
            id=ImageId(generate_id()),
            data=img_bytes,
            metadata=ImageMetadata(
                width=width,
                height=height,
                format=ImageFormat(format_str.lower()),
                channels=3,
                creation_utc=datetime.now(timezone.utc),
                size_bytes=len(img_bytes),
                mime_type=f"image/{format_str.lower()}",
            ),
            description="Placeholder image",
        )
        
        return image
        
    async def _create_placeholder_audio(
        self,
        duration: float,
        format_str: str,
    ) -> Audio:
        """Create placeholder audio.
        
        Args:
            duration: Audio duration in seconds
            format_str: Audio format
            
        Returns:
            Placeholder audio
        """
        # Import libraries only when needed
        import numpy as np
        import soundfile as sf
        
        # Create a silent audio
        sample_rate = 44100
        samples = int(duration * sample_rate)
        audio_data = np.zeros(samples, dtype=np.float32)
        
        # Save to a temporary file
        with tempfile.NamedTemporaryFile(suffix=f".{format_str}", delete=False) as temp_file:
            temp_path = temp_file.name
            
        try:
            # Write the audio data
            sf.write(temp_path, audio_data, sample_rate)
            
            # Read the file as bytes
            with open(temp_path, "rb") as f:
                audio_bytes = f.read()
                
            # Create audio
            audio = Audio(
                id=AudioId(generate_id()),
                data=audio_bytes,
                metadata=AudioMetadata(
                    duration_seconds=duration,
                    format=AudioFormat(format_str.lower()),
                    sample_rate=sample_rate,
                    channels=1,
                    creation_utc=datetime.now(timezone.utc),
                    size_bytes=len(audio_bytes),
                    mime_type=f"audio/{format_str.lower()}",
                ),
                description="Placeholder audio",
            )
            
            return audio
            
        finally:
            # Remove the temporary file
            os.unlink(temp_path)
            
    async def _create_placeholder_video(
        self,
        width: int,
        height: int,
        duration: float,
        format_str: str,
    ) -> Video:
        """Create a placeholder video.
        
        Args:
            width: Video width
            height: Video height
            duration: Video duration in seconds
            format_str: Video format
            
        Returns:
            Placeholder video
        """
        # Import libraries only when needed
        import cv2
        import numpy as np
        
        # Create a temporary file
        with tempfile.NamedTemporaryFile(suffix=f".{format_str}", delete=False) as temp_file:
            temp_path = temp_file.name
            
        try:
            # Create a video writer
            fourcc = cv2.VideoWriter_fourcc(*"XVID")
            fps = 30
            out = cv2.VideoWriter(temp_path, fourcc, fps, (width, height))
            
            # Create frames
            for i in range(int(duration * fps)):
                # Create a blank frame
                frame = np.zeros((height, width, 3), dtype=np.uint8)
                frame[:] = (200, 200, 200)
                
                # Add text
                text = "Placeholder Video"
                font = cv2.FONT_HERSHEY_SIMPLEX
                text_size = cv2.getTextSize(text, font, 1, 2)[0]
                text_x = (width - text_size[0]) // 2
                text_y = (height + text_size[1]) // 2
                cv2.putText(frame, text, (text_x, text_y), font, 1, (0, 0, 0), 2)
                
                # Write the frame
                out.write(frame)
                
            # Release the video writer
            out.release()
            
            # Read the file as bytes
            with open(temp_path, "rb") as f:
                video_bytes = f.read()
                
            # Create video
            video = Video(
                id=VideoId(generate_id()),
                data=video_bytes,
                metadata=VideoMetadata(
                    width=width,
                    height=height,
                    duration_seconds=duration,
                    format=VideoFormat(format_str.lower()),
                    fps=fps,
                    creation_utc=datetime.now(timezone.utc),
                    size_bytes=len(video_bytes),
                    mime_type=f"video/{format_str.lower()}",
                    has_audio=False,
                ),
                description="Placeholder video",
            )
            
            return video
            
        finally:
            # Remove the temporary file
            os.unlink(temp_path)
