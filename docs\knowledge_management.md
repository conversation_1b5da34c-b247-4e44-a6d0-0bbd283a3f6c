# Gerenciamento de Conhecimento

Este documento descreve a funcionalidade de gerenciamento de conhecimento no framework Parlant.

## Visão Geral

O módulo de gerenciamento de conhecimento fornece um conjunto abrangente de ferramentas para armazenar, recuperar e raciocinar sobre conhecimento. Inclui:

1. Uma base de conhecimento para armazenar conhecimento estruturado e não estruturado
2. Um grafo de conhecimento para representar relacionamentos entre itens de conhecimento
3. Um motor de raciocínio para responder perguntas e gerar inferências
4. Suporte para conhecimento multimodal (texto, código, dados estruturados, etc.)

## Componentes

### Base de Conhecimento

A classe `KnowledgeBase` fornece funcionalidade para armazenar e recuperar itens de conhecimento:

- Criação de itens de conhecimento de diferentes tipos
- Atualização e exclusão de itens de conhecimento
- Busca de itens de conhecimento usando similaridade vetorial
- Filtragem de itens de conhecimento por tipo, fonte e tags

Exemplo de uso:

```python
from parlat.knowledge import KnowledgeBase, KnowledgeItemType, KnowledgeItemSource
from parlat.core.loggers import ConsoleLogger
from parlat.adapters.vector_db.chroma import ChromaDatabase
from parlat.adapters.db.transient import TransientDocumentDatabase
from parlat.core.nlp.embedding import EmbedderFactory

# Create a knowledge base
async with KnowledgeBase(
    vector_db=chroma_db,
    document_db=document_db,
    embedder_type=embedder_type,
    embedder_factory=embedder_factory,
    logger=ConsoleLogger(),
) as kb:
    # Create a knowledge item
    item = await kb.create_item(
        title="Python Programming",
        content="Python is a high-level programming language.",
        type=KnowledgeItemType.TEXT,
        source=KnowledgeItemSource.USER,
        metadata={"author": "John Doe"},
        tags=["programming", "python"],
    )
    
    # Retrieve a knowledge item
    item = await kb.read_item(item.id)
    
    # Update a knowledge item
    item = await kb.update_item(
        item_id=item.id,
        title="Python Programming Language",
        content="Python is a high-level, interpreted programming language.",
    )
    
    # Search for knowledge items
    results = await kb.search_items(
        query="programming language",
        k=5,
        type=KnowledgeItemType.TEXT,
        tags=["python"],
    )
```

### Grafo de Conhecimento

A classe `KnowledgeGraph` fornece funcionalidade para representar relacionamentos entre itens de conhecimento:

- Adição e remoção de relacionamentos
- Obtenção de vizinhos de um item de conhecimento
- Encontrar caminhos entre itens de conhecimento
- Extração de subgrafos para visualização

Exemplo de uso:

```python
from parlat.knowledge import KnowledgeGraph
from parlat.core.loggers import ConsoleLogger

# Create a knowledge graph
graph = KnowledgeGraph(
    knowledge_base=kb,
    logger=ConsoleLogger(),
)

# Initialize the graph
await graph.initialize()

# Add a relationship
await graph.add_relationship(
    source_id=item1.id,
    target_id=item2.id,
    relationship_type="related_to",
)

# Get neighbors of a knowledge item
neighbors = await graph.get_neighbors(
    item_id=item1.id,
    relationship_type="related_to",
    direction="outgoing",
)

# Get a path between knowledge items
path = await graph.get_path(
    source_id=item1.id,
    target_id=item3.id,
)

# Get a subgraph for visualization
subgraph = await graph.get_subgraph(
    item_ids=[item1.id, item2.id, item3.id],
    include_neighbors=True,
)
```

### Motor de Raciocínio

A classe `KnowledgeReasoner` fornece funcionalidade para raciocinar sobre a base de conhecimento:

- Responder perguntas usando itens de conhecimento relevantes
- Gerar inferências sobre itens de conhecimento
- Validar relacionamentos entre itens de conhecimento

Exemplo de uso:

```python
from parlat.knowledge import KnowledgeReasoner
from parlat.core.loggers import ConsoleLogger

# Create a knowledge reasoner
reasoner = KnowledgeReasoner(
    knowledge_base=kb,
    knowledge_graph=graph,
    nlp_service=nlp_service,
    logger=ConsoleLogger(),
)

# Answer a question
result = await reasoner.answer_question(
    question="What is Python used for?",
    max_items=5,
)

# Generate an inference
result = await reasoner.generate_inference(
    item_id=item.id,
    max_related_items=3,
)

# Validate a relationship
result = await reasoner.validate_relationship(
    source_id=item1.id,
    target_id=item2.id,
    relationship_type="related_to",
)
```

### Gerenciador de Conhecimento

A classe `KnowledgeManager` fornece uma interface unificada para gerenciamento de conhecimento:

- Adição de diferentes tipos de conhecimento (texto, código, dados estruturados)
- Relacionamento de itens de conhecimento
- Busca de conhecimento
- Resposta a perguntas
- Obtenção de redes de conhecimento para visualização

Exemplo de uso:

```python
from parlat.knowledge import KnowledgeManager
from parlat.core.loggers import ConsoleLogger

# Create a knowledge manager
async with KnowledgeManager(
    vector_db=vector_db,
    document_db=document_db,
    embedder_type=embedder_type,
    embedder_factory=embedder_factory,
    nlp_service=nlp_service,
    logger=ConsoleLogger(),
) as manager:
    # Add text knowledge
    item1 = await manager.add_text_knowledge(
        title="Python Programming",
        content="Python is a high-level programming language.",
        tags=["programming", "python"],
    )
    
    # Add code knowledge
    item2 = await manager.add_code_knowledge(
        title="Hello World",
        code="print('Hello, World!')",
        language="python",
        tags=["python", "example"],
    )
    
    # Add structured knowledge
    item3 = await manager.add_structured_knowledge(
        title="Python Features",
        data={
            "name": "Python",
            "version": "3.9",
            "features": ["dynamic typing", "garbage collection"],
        },
        tags=["python", "features"],
    )
    
    # Relate knowledge items
    await manager.relate_knowledge(
        source_id=item1.id,
        target_id=item2.id,
        relationship_type="has_example",
    )
    
    # Search for knowledge
    results = await manager.search_knowledge(
        query="python programming",
        k=5,
        tags=["python"],
    )
    
    # Answer a question
    result = await manager.answer_question(
        question="What is Python?",
        max_items=5,
    )
    
    # Get a knowledge network for visualization
    network = await manager.get_knowledge_network(
        item_ids=[item1.id, item2.id, item3.id],
        include_neighbors=True,
    )
```

## Tipos de Itens de Conhecimento

O módulo de gerenciamento de conhecimento suporta diferentes tipos de itens de conhecimento:

- `TEXT`: Conhecimento textual (artigos, notas, etc.)
- `CODE`: Trechos de código com metadados de linguagem
- `IMAGE`: Dados de imagem com metadados
- `AUDIO`: Dados de áudio com metadados
- `VIDEO`: Dados de vídeo com metadados

## Integração com Parlant

A funcionalidade de gerenciamento de conhecimento está integrada com o framework Parlant:

1. **Sistema de Agente**: Os agentes podem acessar e contribuir para a base de conhecimento
2. **Processamento de Linguagem Natural**: O conhecimento é indexado e pesquisável usando técnicas de NLP
3. **Armazenamento de Dados**: O conhecimento é armazenado de forma eficiente em bancos de dados vetoriais e documentais
4. **Ferramentas**: Ferramentas especializadas para manipulação de conhecimento

## Detalhes de Implementação

### Armazenamento de Dados

O módulo de gerenciamento de conhecimento usa:

- Banco de dados vetorial para busca semântica
- Banco de dados documental para metadados e relacionamentos
- Sistema de arquivos para dados binários (imagens, áudio, vídeo)

### Processo de Conhecimento

O processo de gerenciamento de conhecimento segue estas etapas:

1. **Aquisição**: Conhecimento é adquirido de várias fontes
2. **Processamento**: O conhecimento é processado e indexado
3. **Armazenamento**: O conhecimento é armazenado de forma eficiente
4. **Recuperação**: O conhecimento pode ser recuperado usando vários métodos
5. **Raciocínio**: Inferências podem ser feitas sobre o conhecimento
6. **Atualização**: O conhecimento pode ser atualizado conforme necessário

### Considerações de Privacidade

O módulo de gerenciamento de conhecimento inclui:

- Controle de acesso granular para itens de conhecimento
- Criptografia de dados sensíveis
- Registro de acesso e modificações
- Políticas de retenção de dados

## Melhorias Futuras

Possíveis melhorias futuras para o módulo de gerenciamento de conhecimento:

1. **Aprendizado Contínuo**: Permitir que a base de conhecimento aprenda continuamente
2. **Raciocínio Avançado**: Implementar técnicas mais sofisticadas de raciocínio
3. **Integração Multimodal**: Melhorar o suporte para conhecimento multimodal
4. **Escalabilidade**: Otimizar para grandes volumes de conhecimento
5. **Federação**: Suportar bases de conhecimento federadas
