[tool.poetry]
name = "parlant"
version = "2.0.0"
license = "Apache-2.0"
description = ""
authors = ["<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"]
packages = [{ include = "parlant", from = "src" }]
readme = "README.md"

[tool.poetry.scripts]
parlant = "parlant.bin.client:main"
parlant-server = "parlant.bin.server:main"
parlant-prepare-migration = "parlant.bin.prepare_migration:main"

[tool.poetry.dependencies]
aiofiles = "^24.1.0"
aiopenapi3 = "^0.6.0"
aiorwlock = "^1.5.0"
boto3 = "^1.35.70"
chromadb = "0.5.17"
click = "^8.1.7"
click-completion = "^0.5.2"
colorama = "^0.4.6"
coloredlogs = "^15.0.1"
contextvars = "^2.4"
croniter = "^5.0.1"
fastapi = "^0.115.5"
httpx = "^0.27.0"
jsonfinder = "^0.4.2"
jsonschema = "^4.23.0"
lagom = "^2.6.0"
more-itertools = ">=10.3.0"
nano-vectordb = "^*******"
nanoid = "^2.0.0"
networkx = { extras = ["default"], version = "^3.3" }
openai = "^1.45.0"
openapi3-parser = "^1.1.17"
opentelemetry-exporter-otlp-proto-grpc = "1.27.0"
#parlant-client = "^0.10.3" # Use this when releasing to main
parlant-client = { git = "https://github.com/emcie-co/parlant-client-python.git", tag = "develop.1743678521" }
python = "^3.10"
python-dateutil = "^2.8.2"
python-dotenv = "^1.0.1"
requests = "^2.32.3"
rich = "^13.7.1"
semver = "^3.0.2"
structlog = "^24.4.0"
tabulate = "^0.9.0"
tenacity = "^8.4.2"
tiktoken = "^0.8"
tokenizers = "^0.20"
toml = "^0.10.2"
types-aiofiles = "^24.1.0.20240626"
types-croniter = "^4.0.0.20241030"
types-jsonschema = "^4.22.0.20240610"
uvicorn = "^0.32.1"

# --- optional packages ---
anthropic = { version = "^0.37.1", optional = true }
cerebras-cloud-sdk = { version = "^1.25.0", optional = true }
google-api-core = { version = "^2.24.1", optional = true }
google-genai = { version = "^1.2.0", optional = true }
together = { version = "^1.2.12", optional = true }
torch = { version = "^2.5.1", optional = true }
transformers = { version = "^4.46.2", optional = true }
litellm = { version = "^1.61.16", optional = true }
pymongo = { version = "^4.11.1", optional = true }
jinja2 = "^3.1.6"


[tool.poetry.group.dev.dependencies]
ipython = "^8.26.0"
mypy = "^1.11.1"
pep8-naming = "^0.13.3"
pytest = "^8.0.0"
pytest-asyncio = "^0.23.5"
pytest-bdd = "^7.1.2"
pytest-cov = "^5.0.0"
pytest-stochastics = { git = "https://github.com/emcie-co/pytest-stochastics.git", tag = "v0.5.2" }
pytest-tap = "^3.4"
pytest-timing = { git = "https://github.com/emcie-co/mc-spitfyre.git", subdirectory = "pytest-timing", tag = "timing_v0.1.4" }
python-dotenv = "^1.0.1"
ruff = "^0.5.6"
types-python-dateutil = "^2.8.19.20240106"
types-requests = "^2.32.0.20240712"
pytest-xdist = "^3.6.1"

[tool.poetry.extras]
anthropic = ["anthropic", "torch", "transformers"]

aws = ["anthropic", "transformers", "torch"]

together = ["torch", "together", "transformers"]

cerebras = ["cerebras-cloud-sdk", "torch", "transformers"]

deepseek = ["torch", "transformers"]

gemini = ["google-genai", "google-api-core", "torch"]

litellm = ["litellm", "torch", "transformers"]

mongo = ["pymongo"]


[build-system]
build-backend = "poetry.core.masonry.api"
requires = ["poetry-core"]
