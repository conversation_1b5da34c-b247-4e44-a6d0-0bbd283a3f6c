# Processamento e Análise de Dados

Este documento descreve a funcionalidade de processamento e análise de dados no framework Parlant.

## Visão Geral

O módulo de processamento e análise de dados fornece um conjunto abrangente de ferramentas para trabalhar com dados, incluindo:

1. Carregamento de dados de vários formatos
2. Pré-processamento e limpeza de dados
3. Análise de dados tabulares
4. Visualização de dados
5. Integração com machine learning

## Componentes

### Carregamento de Dados

A classe `DataLoader` fornece funcionalidade para carregar dados de vários formatos de arquivo:

- CSV
- JSON
- Excel
- Parquet
- SQL
- Texto
- XML
- HTML
- YAML

Exemplo de uso:

```python
from parlat.data import DataLoader, DataLoaderOptions
from parlat.core.loggers import ConsoleLogger

# Create a data loader
loader = DataLoader(ConsoleLogger())

# Configure options
options = DataLoaderOptions(
    encoding="utf-8",
    header=True,
    skip_rows=0,
    max_rows=None,
    delimiter=",",
)

# Load data from a CSV file
df = loader.load_file("data.csv", options)
```

### Pré-processamento de Dados

A classe `DataCleaner` fornece funcionalidade para limpar e pré-processar dados:

- Tratamento de valores ausentes
- Detecção e tratamento de outliers
- Conversão de tipos de dados
- Limpeza de dados de texto
- Remoção de duplicatas

Exemplo de uso:

```python
from parlat.data import DataCleaner, DataCleaningOptions
from parlat.core.loggers import ConsoleLogger

# Create a data cleaner
cleaner = DataCleaner(ConsoleLogger())

# Configure options
options = DataCleaningOptions(
    handle_missing=True,
    imputation_strategy="mean",
    handle_outliers=True,
    outlier_method="z_score",
    outlier_threshold=3.0,
    convert_dtypes=True,
    clean_text=True,
    remove_duplicates=True,
)

# Clean data
cleaned_df = cleaner.clean_data(df, options)
```

### Análise de Dados

A classe `DataAnalyzer` fornece funcionalidade para analisar dados tabulares:

- Estatísticas descritivas
- Análise de correlação
- Importância de features

Exemplo de uso:

```python
from parlat.data import DataAnalyzer, DataAnalysisOptions
from parlat.core.loggers import ConsoleLogger

# Create a data analyzer
analyzer = DataAnalyzer(ConsoleLogger())

# Configure options
options = DataAnalysisOptions(
    include_numeric=True,
    include_categorical=True,
    correlation_method="pearson",
    correlation_min_value=0.3,
    target_column="target",
    feature_importance_method="random_forest",
)

# Analyze data
analysis = analyzer.analyze_data(df, options)

# Access analysis results
print(f"Data shape: {analysis['shape']}")
print(f"Missing values: {analysis['missing_values']['total_missing']}")
print(f"Numeric statistics: {analysis['numeric_stats']}")
print(f"Categorical statistics: {analysis['categorical_stats']}")
print(f"Correlation: {analysis['correlation']}")
print(f"Feature importance: {analysis['feature_importance']}")
```

### Visualização de Dados

A classe `DataVisualizer` fornece funcionalidade para criar visualizações a partir de dados tabulares:

- Gráficos de barras
- Gráficos de linha
- Gráficos de dispersão
- Gráficos de pizza
- Histogramas
- Box plots
- Violin plots
- Mapas de calor
- Gráficos de pares
- Gráficos conjuntos

Exemplo de uso:

```python
from parlat.data import DataVisualizer, VisualizationOptions, ChartType
from parlat.core.loggers import ConsoleLogger

# Create a data visualizer
visualizer = DataVisualizer(ConsoleLogger())

# Configure options
options = VisualizationOptions(
    title="Age Distribution",
    x_label="Age",
    y_label="Count",
    figsize=(10, 6),
    palette="viridis",
    grid=True,
    legend=True,
)

# Create a bar chart
chart = visualizer.create_visualization(
    df,
    ChartType.BAR,
    x="age_group",
    y="count",
    options=options,
)

# The chart is returned as a base64-encoded image
# You can display it in a web interface or save it to a file
```

### Machine Learning

A classe `ModelTrainer` fornece funcionalidade para treinar e avaliar modelos de machine learning:

- Classificação
- Regressão
- Clustering
- Avaliação de modelos
- Importância de features
- Salvamento e carregamento de modelos

Exemplo de uso:

```python
from parlat.data import ModelTrainer, ModelTrainingOptions, ModelType
from parlat.core.loggers import ConsoleLogger

# Create a model trainer
trainer = ModelTrainer(ConsoleLogger())

# Configure options
options = ModelTrainingOptions(
    test_size=0.2,
    random_state=42,
    handle_categorical=True,
    handle_missing=True,
    normalize=True,
    cross_validation=True,
    n_folds=5,
)

# Train a classification model
model_result = trainer.train_model(
    df,
    target_column="target",
    model_type=ModelType.RANDOM_FOREST,
    options=options,
)

# Access model and evaluation results
print(f"Model type: {model_result['model_type']}")
print(f"Is classifier: {model_result['is_classifier']}")
print(f"Feature names: {model_result['feature_names']}")
print(f"Evaluation: {model_result['evaluation']}")
print(f"Feature importance: {model_result['feature_importance']}")

# Make predictions
predictions = trainer.predict(model_result, new_data)

# Save the model
trainer.save_model(model_result, "model.pkl")

# Load the model
loaded_model = trainer.load_model("model.pkl")
```

## Integração com Parlant

A funcionalidade de processamento e análise de dados está integrada com o framework Parlant:

1. **Ferramentas de Agente**: Os componentes de processamento de dados podem ser usados como ferramentas para agentes
2. **Componentes de UI**: Os componentes de visualização podem ser usados na interface do usuário
3. **Análise**: Os componentes de análise de dados podem ser usados para analisar o comportamento dos agentes
4. **Machine Learning**: Os componentes de machine learning podem ser usados para aprimorar as capacidades dos agentes

## Dependências

O módulo de processamento e análise de dados depende das seguintes bibliotecas:

- pandas: Para manipulação de dados
- numpy: Para operações numéricas
- matplotlib: Para criar visualizações
- seaborn: Para visualizações aprimoradas
- scikit-learn: Para machine learning
- kneed: Para encontrar o número ótimo de clusters

## Detalhes de Implementação

### Processamento de Dados

O módulo de processamento de dados inclui:

- Carregamento eficiente de grandes conjuntos de dados
- Tratamento automático de tipos de dados
- Validação de dados de entrada
- Tratamento de erros robusto

### Análise de Dados

O módulo de análise de dados fornece:

- Análise estatística abrangente
- Detecção automática de padrões
- Geração de relatórios
- Exportação de resultados

### Visualização de Dados

O módulo de visualização de dados oferece:

- Temas personalizáveis
- Layouts responsivos
- Interatividade
- Exportação em vários formatos

## Melhorias Futuras

Possíveis melhorias futuras para o módulo de processamento e análise de dados:

1. **Processamento em Tempo Real**: Adicionar suporte para processamento de dados em streaming
2. **Análise Avançada**: Implementar técnicas mais sofisticadas de análise
3. **Visualização Interativa**: Melhorar a interatividade das visualizações
4. **Integração com Big Data**: Adicionar suporte para processamento distribuído
5. **AutoML**: Implementar seleção automática de modelos e otimização de hiperparâmetros
